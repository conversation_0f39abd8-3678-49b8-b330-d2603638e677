# interior-design

# 📁 Complete File-by-File Setup Guide

## 🚀 Setup Instructions

1. **Clone your repository**: `git clone https://github.com/dk7696822/interior-design.git`
2. **Navigate to project**: `cd interior-design`
3. **Create the files below** in the exact structure shown
4. **Copy the code** from the artifacts into each file

## 📂 Root Level Files

### `.gitignore`
```gitignore
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/

# Build outputs
build/
dist/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# AWS
.aws/

# Database
*.db
*.sqlite

# Uploads
uploads/
temp/

# PM2
ecosystem.config.js.bak
```

### `README.md`
```markdown
# 🏡 Interior Design Platform

A complete interior design platform with customer-facing website, admin portal, and WhatsApp integration.

## 🚀 Features

### Customer App
- 🛍️ Product catalog with search and filters
- 👤 User authentication and profiles
- 🛒 Shopping cart and wishlist
- 📞 Consultation booking
- 📱 WhatsApp integration
- 📊 Project tracking

### Portal App
- 📊 Admin dashboard with analytics
- 📦 Product and category management
- 👥 Customer and project management
- 💬 WhatsApp conversation management
- 📈 Sales and performance analytics
- 📁 File upload and media management

## 🛠️ Tech Stack

- **Backend**: Node.js, Express, PostgreSQL, Auth0, AWS S3
- **Frontend**: React 18, Tailwind CSS, Framer Motion
- **Authentication**: Auth0 with RBAC
- **Database**: PostgreSQL with optimized schema
- **Storage**: AWS S3 for images and documents

## 🏃‍♂️ Quick Start

1. **Install dependencies**: `npm run install:all`
2. **Configure environment**: Copy `.env.example` files and update
3. **Setup database**: `npm run migrate`
4. **Start development**: `npm run start:backend`, `npm run start:customer`, `npm run start:portal`

## 📚 Documentation

- [Setup Guide](docs/SETUP.md)
- [API Documentation](docs/API.md) 
- [Deployment Guide](docs/DEPLOYMENT.md)

## 📝 License

MIT License
```

### `package.json`
```json
{
  "name": "interior-design-platform",
  "version": "1.0.0",
  "description": "Complete interior design platform",
  "private": true,
  "scripts": {
    "install:all": "npm run install:backend && npm run install:customer && npm run install:portal",
    "install:backend": "cd backend && npm install",
    "install:customer": "cd customer-app && npm install", 
    "install:portal": "cd portal-app && npm install",
    "start:backend": "cd backend && npm run dev",
    "start:customer": "cd customer-app && npm start",
    "start:portal": "cd portal-app && npm start",
    "migrate": "cd backend && node src/database/migrate.js migrate"
  },
  "workspaces": ["backend", "customer-app", "portal-app"],
  "keywords": ["interior-design", "react", "nodejs"],
  "author": "Interior Design Platform Team",
  "license": "MIT"
}
```

## 📁 Backend Files

### `backend/package.json`
**Copy from artifact**: `backend_package_json`

### `backend/.env.example`
**Copy from artifact**: `env_example`

### `backend/server.js`
**Copy from artifact**: `server_js`

### `backend/src/config/auth0.js`
```javascript
// This file would contain Auth0 configuration
// Currently integrated into middleware/auth.js
module.exports = {
  domain: process.env.AUTH0_DOMAIN,
  audience: process.env.AUTH0_AUDIENCE,
  clientId: process.env.AUTH0_CLIENT_ID,
  clientSecret: process.env.AUTH0_CLIENT_SECRET
};
```

### `backend/src/middleware/auth.js`
**Copy from artifact**: `auth0_middleware`

### `backend/src/middleware/errorHandler.js`
**Copy from artifact**: `error_handler`

### `backend/src/database/connection.js`
**Copy from artifact**: `database_connection`

### `backend/src/database/migration.js`
**Copy from artifact**: `database_migration`

### `backend/src/database/migrate.js`
**Copy from artifact**: `migration_runner`

### `backend/src/models/index.js`
**Copy from artifact**: `models_index`

### `backend/src/models/User.js`
**Copy from artifact**: `user_model`

### `backend/src/models/Category.js`
**Copy from artifact**: `category_model` (Category Model section)

### `backend/src/models/Product.js`
**Copy from artifact**: `product_model`

### `backend/src/models/ProductImage.js`
**Copy from artifact**: `product_image_model`

### `backend/src/models/WhatsApp.js`
**Copy from artifact**: `whatsapp_models`

### `backend/src/routes/auth.js`
**Copy from artifact**: `auth_routes`

### `backend/src/routes/categories.js`
**Copy from artifact**: `category_routes`

### `backend/src/routes/products.js`
**Copy from artifact**: `product_routes`

### `backend/src/routes/whatsapp.js`
**Copy from artifact**: `whatsapp_routes`

### `backend/src/routes/upload.js`
**Copy from artifact**: `upload_routes`

### `backend/src/services/s3Service.js`
**Copy from artifact**: `s3_service`

### `backend/src/services/whatsappService.js`
**Copy from artifact**: `whatsapp_service`

### `backend/src/utils/logger.js`
**Copy from artifact**: `logger_utility`

## 📁 Customer App Files

### `customer-app/package.json`
**Copy from artifact**: `customer_app_package`

### `customer-app/.env.example`
```bash
REACT_APP_AUTH0_DOMAIN=your-domain.auth0.com
REACT_APP_AUTH0_CLIENT_ID=your_customer_app_client_id
REACT_APP_AUTH0_AUDIENCE=https://interior-design-api
REACT_APP_API_BASE_URL=http://localhost:5000/api/v1
REACT_APP_WHATSAPP_NUMBER=+91XXXXXXXXXX
```

### `customer-app/src/config/auth0.js`
**Copy from artifact**: `customer_app_auth0`

### `customer-app/src/services/api.js`
**Copy from artifact**: `customer_app_api`

### `customer-app/src/context/AuthContext.jsx`
**Copy from artifact**: `customer_app_context` (AuthContext section)

### `customer-app/src/context/CartContext.jsx`
**Copy from artifact**: `customer_app_context` (CartContext section)

### `customer-app/src/context/WishlistContext.jsx`
**Copy from artifact**: `customer_app_context` (WishlistContext section)

### `customer-app/src/components/common/Header.jsx`
**Copy from artifact**: `customer_app_components` (Header section)

### `customer-app/src/components/common/Footer.jsx`
**Copy from artifact**: `customer_app_components` (Footer section)

### `customer-app/src/components/common/LoadingSpinner.jsx`
**Copy from artifact**: `customer_app_components` (LoadingSpinner section)

### `customer-app/src/components/common/WhatsAppButton.jsx`
**Copy from artifact**: `customer_app_components` (WhatsAppButton section)

### `customer-app/src/components/catalog/ProductCard.jsx`
**Copy from artifact**: `customer_app_pages` (ProductCard section)

### `customer-app/src/components/auth/ProtectedRoute.jsx`
**Copy from artifact**: `customer_app_main` (ProtectedRoute section)

### `customer-app/src/components/auth/AuthCallback.jsx`
**Copy from artifact**: `customer_app_main` (AuthCallback section)

### `customer-app/src/pages/Home.jsx`
**Copy from artifact**: `customer_app_pages` (Home section)

### `customer-app/src/App.jsx`
**Copy from artifact**: `customer_app_main` (App section)

### `customer-app/src/index.js`
**Copy from artifact**: `customer_app_main` (index.js section)

### `customer-app/src/reportWebVitals.js`
**Copy from artifact**: `customer_app_main` (reportWebVitals section)

### `customer-app/public/index.html`
```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Interior Design Platform - Transform your space with expert design" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>InteriorCraft - Interior Design Platform</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
```

### `customer-app/tailwind.config.js`
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [],
}
```

### `customer-app/src/index.css`
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
      monospace;
  }
}

@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}
```

## 📁 Portal App Files

### `portal-app/package.json`
**Copy from artifact**: `portal_app_package`

### `portal-app/.env.example`
```bash
REACT_APP_AUTH0_DOMAIN=your-domain.auth0.com
REACT_APP_AUTH0_PORTAL_CLIENT_ID=your_portal_app_client_id
REACT_APP_AUTH0_AUDIENCE=https://interior-design-api
REACT_APP_API_BASE_URL=http://localhost:5000/api/v1
REACT_APP_CUSTOMER_APP_URL=http://localhost:3000
```

### `portal-app/src/services/api.js`
**Copy from artifact**: `portal_app_context` (API service section)

### `portal-app/src/context/AuthContext.jsx`
**Copy from artifact**: `portal_app_context` (AuthContext section)

### `portal-app/src/context/DataContext.jsx`
**Copy from artifact**: `portal_app_context` (DataContext section)

### `portal-app/src/hooks/usePermissions.js`
**Copy from artifact**: `portal_app_context` (usePermissions section)

### `portal-app/src/config/auth0.js`
**Copy from artifact**: `portal_app_main` (auth0Config section)

### `portal-app/src/components/layout/Sidebar.jsx`
**Copy from artifact**: `portal_app_components` (Sidebar section)

### `portal-app/src/components/layout/Header.jsx`
**Copy from artifact**: `portal_app_components` (Header section)

### `portal-app/src/components/common/DataTable.jsx`
**Copy from artifact**: `portal_app_components` (DataTable section)

### `portal-app/src/components/common/Modal.jsx`
**Copy from artifact**: `portal_app_components` (Modal section)

### `portal-app/src/components/common/LoadingSpinner.jsx`
**Copy from artifact**: `portal_app_components` (LoadingSpinner section)

### `portal-app/src/components/common/StatusBadge.jsx`
**Copy from artifact**: `portal_app_components` (StatusBadge section)

### `portal-app/src/components/auth/ProtectedRoute.jsx`
**Copy from artifact**: `portal_app_main` (ProtectedRoute section)

### `portal-app/src/components/auth/AuthCallback.jsx`
**Copy from artifact**: `portal_app_main` (AuthCallback section)

### `portal-app/src/pages/Dashboard.jsx`
**Copy from artifact**: `portal_app_dashboard`

### `portal-app/src/App.jsx`
**Copy from artifact**: `portal_app_main` (App section)

### `portal-app/src/index.js`
```javascript
import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
```

### `portal-app/src/index.css`
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f9fafb;
  }

  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
      monospace;
  }
}
```

### `portal-app/tailwind.config.js`
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [],
}
```

### `portal-app/public/index.html`
```html
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Interior Design Portal - Admin Dashboard" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>InteriorCraft Portal - Admin Dashboard</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
```

## 📁 Documentation Files

### `docs/SETUP.md`
**Copy from artifact**: `react_setup_guide`

### `docs/API.md`
```markdown
# 📡 API Documentation

## Authentication
All protected endpoints require JWT token in Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Endpoints

### Products
- `GET /api/v1/products` - Get products with pagination
- `GET /api/v1/products/:id` - Get product by ID
- `POST /api/v1/products` - Create product (Admin)
- `PUT /api/v1/products/:id` - Update product (Admin)

### Categories  
- `GET /api/v1/categories` - Get categories
- `POST /api/v1/categories` - Create category (Admin)

### WhatsApp
- `GET /api/v1/whatsapp/conversations` - Get conversations (Staff)
- `POST /api/v1/whatsapp/conversations/:id/messages` - Send message (Staff)

See backend source code for complete API documentation.
```

### `docs/DEPLOYMENT.md`
**Copy from artifact**: `whatsapp_setup_guide` (Deployment sections)

## 🚀 Setup Commands

After creating all files:

```bash
# 1. Install all dependencies
npm run install:all

# 2. Set up environment files
cp backend/.env.example backend/.env
cp customer-app/.env.example customer-app/.env  
cp portal-app/.env.example portal-app/.env

# 3. Configure your environment variables in the .env files

# 4. Run database migration
npm run migrate

# 5. Start all services (in separate terminals)
npm run start:backend   # Terminal 1 - http://localhost:5000
npm run start:customer  # Terminal 2 - http://localhost:3000  
npm run start:portal    # Terminal 3 - http://localhost:3001
```

## ✅ File Checklist

Make sure you have created these key files:

**Root:**
- [ ] `.gitignore`
- [ ] `README.md` 
- [ ] `package.json`

**Backend (18 files):**
- [ ] `backend/package.json`
- [ ] `backend/.env.example`
- [ ] `backend/server.js`
- [ ] `backend/src/middleware/auth.js`
- [ ] `backend/src/database/connection.js`
- [ ] `backend/src/models/User.js`
- [ ] (+ 12 more backend files)

**Customer App (12 files):**
- [ ] `customer-app/package.json`
- [ ] `customer-app/src/App.jsx`
- [ ] `customer-app/src/pages/Home.jsx`
- [ ] `customer-app/src/context/AuthContext.jsx`
- [ ] (+ 8 more customer app files)

**Portal App (13 files):**
- [ ] `portal-app/package.json` 
- [ ] `portal-app/src/App.jsx`
- [ ] `portal-app/src/pages/Dashboard.jsx`
- [ ] `portal-app/src/components/layout/Sidebar.jsx`
- [ ] (+ 9 more portal app files)

**Documentation:**
- [ ] `docs/SETUP.md`
- [ ] `docs/API.md`
- [ ] `docs/DEPLOYMENT.md`

This gives you the complete file structure with exact filenames and locations! 🎉