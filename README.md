# Interior Design Platform - Project Structure

```
interior-design-platform/
│
├── backend/                          # Node.js Backend (Deploy to EC2)
│   ├── src/
│   │   ├── config/
│   │   │   ├── database.js           # PostgreSQL connection
│   │   │   ├── aws.js                # AWS S3 configuration
│   │   │   ├── whatsapp.js           # WhatsApp API config
│   │   │   └── app.js                # Express app configuration
│   │   ├── controllers/
│   │   │   ├── auth.js               # Authentication
│   │   │   ├── products.js           # Product management
│   │   │   ├── categories.js         # Category management
│   │   │   ├── projects.js           # Project/consultation management
│   │   │   ├── customers.js          # Customer management
│   │   │   ├── whatsapp.js           # WhatsApp integration
│   │   │   ├── upload.js             # File upload to S3
│   │   │   └── analytics.js          # Analytics and reports
│   │   ├── middleware/
│   │   │   ├── auth.js               # JWT authentication
│   │   │   ├── upload.js             # File upload middleware
│   │   │   ├── validation.js         # Request validation
│   │   │   └── rateLimiter.js        # API rate limiting
│   │   ├── models/
│   │   │   ├── User.js               # User model
│   │   │   ├── Product.js            # Product model
│   │   │   ├── Category.js           # Category model
│   │   │   ├── Project.js            # Project model
│   │   │   └── WhatsAppConversation.js
│   │   ├── routes/
│   │   │   ├── auth.js               # Authentication routes
│   │   │   ├── products.js           # Product routes
│   │   │   ├── categories.js         # Category routes
│   │   │   ├── projects.js           # Project routes
│   │   │   ├── customers.js          # Customer routes
│   │   │   ├── whatsapp.js           # WhatsApp routes
│   │   │   ├── upload.js             # File upload routes
│   │   │   └── portal.js             # Internal portal routes
│   │   ├── services/
│   │   │   ├── emailService.js       # Email notifications
│   │   │   ├── whatsappService.js    # WhatsApp API service
│   │   │   ├── s3Service.js          # AWS S3 operations
│   │   │   ├── authService.js        # Authentication logic
│   │   │   └── analyticsService.js   # Analytics calculations
│   │   ├── utils/
│   │   │   ├── logger.js             # Winston logger
│   │   │   ├── validators.js         # Data validation
│   │   │   ├── helpers.js            # Utility functions
│   │   │   └── constants.js          # Application constants
│   │   └── database/
│   │       ├── migrations/           # Database migrations
│   │       ├── seeds/                # Database seeders
│   │       └── connection.js         # Database connection
│   ├── tests/
│   │   ├── unit/                     # Unit tests
│   │   ├── integration/              # Integration tests
│   │   └── fixtures/                 # Test data
│   ├── package.json
│   ├── package-lock.json
│   ├── .env.example
│   ├── .gitignore
│   ├── Dockerfile                    # For EC2 deployment
│   ├── ecosystem.config.js           # PM2 configuration
│   └── server.js                     # Entry point
│
├── customer-app/                     # Customer Frontend (Deploy to Amplify)
│   ├── public/
│   │   ├── index.html
│   │   ├── favicon.ico
│   │   └── manifest.json
│   ├── src/
│   │   ├── components/
│   │   │   ├── common/
│   │   │   │   ├── Header.jsx
│   │   │   │   ├── Footer.jsx
│   │   │   │   ├── Loader.jsx
│   │   │   │   ├── Modal.jsx
│   │   │   │   └── WhatsAppButton.jsx
│   │   │   ├── auth/
│   │   │   │   ├── Login.jsx
│   │   │   │   ├── Register.jsx
│   │   │   │   └── ForgotPassword.jsx
│   │   │   ├── catalog/
│   │   │   │   ├── ProductGrid.jsx
│   │   │   │   ├── ProductCard.jsx
│   │   │   │   ├── ProductDetail.jsx
│   │   │   │   ├── CategoryFilter.jsx
│   │   │   │   └── SearchBar.jsx
│   │   │   ├── profile/
│   │   │   │   ├── Profile.jsx
│   │   │   │   ├── ProjectHistory.jsx
│   │   │   │   └── Wishlist.jsx
│   │   │   └── consultation/
│   │   │       ├── ConsultationForm.jsx
│   │   │       ├── ProjectTracker.jsx
│   │   │       └── DesignGallery.jsx
│   │   ├── pages/
│   │   │   ├── Home.jsx
│   │   │   ├── Catalog.jsx
│   │   │   ├── ProductDetails.jsx
│   │   │   ├── Consultation.jsx
│   │   │   ├── Profile.jsx
│   │   │   ├── About.jsx
│   │   │   └── Contact.jsx
│   │   ├── hooks/
│   │   │   ├── useAuth.js
│   │   │   ├── useProducts.js
│   │   │   ├── useProjects.js
│   │   │   └── useWhatsApp.js
│   │   ├── services/
│   │   │   ├── api.js                # Axios configuration
│   │   │   ├── authService.js
│   │   │   ├── productService.js
│   │   │   ├── projectService.js
│   │   │   └── whatsappService.js
│   │   ├── context/
│   │   │   ├── AuthContext.jsx
│   │   │   ├── ProductContext.jsx
│   │   │   └── ThemeContext.jsx
│   │   ├── utils/
│   │   │   ├── constants.js
│   │   │   ├── helpers.js
│   │   │   └── validators.js
│   │   ├── styles/
│   │   │   ├── globals.css
│   │   │   ├── components.css
│   │   │   └── responsive.css
│   │   ├── assets/
│   │   │   ├── images/
│   │   │   ├── icons/
│   │   │   └── fonts/
│   │   ├── App.jsx
│   │   ├── App.css
│   │   └── index.js
│   ├── package.json
│   ├── package-lock.json
│   ├── .env.example
│   ├── .gitignore
│   ├── amplify.yml                   # AWS Amplify build settings
│   └── README.md
│
├── portal-app/                       # Internal Portal Frontend (Deploy to Amplify)
│   ├── public/
│   │   ├── index.html
│   │   ├── favicon.ico
│   │   └── manifest.json
│   ├── src/
│   │   ├── components/
│   │   │   ├── common/
│   │   │   │   ├── Sidebar.jsx
│   │   │   │   ├── Header.jsx
│   │   │   │   ├── DataTable.jsx
│   │   │   │   ├── Charts.jsx
│   │   │   │   └── Modal.jsx
│   │   │   ├── auth/
│   │   │   │   ├── Login.jsx
│   │   │   │   └── ProtectedRoute.jsx
│   │   │   ├── dashboard/
│   │   │   │   ├── Overview.jsx
│   │   │   │   ├── Analytics.jsx
│   │   │   │   └── RecentActivity.jsx
│   │   │   ├── products/
│   │   │   │   ├── ProductList.jsx
│   │   │   │   ├── ProductForm.jsx
│   │   │   │   ├── CategoryManager.jsx
│   │   │   │   └── ImageUploader.jsx
│   │   │   ├── customers/
│   │   │   │   ├── CustomerList.jsx
│   │   │   │   ├── CustomerDetail.jsx
│   │   │   │   └── CustomerForm.jsx
│   │   │   ├── projects/
│   │   │   │   ├── ProjectList.jsx
│   │   │   │   ├── ProjectDetail.jsx
│   │   │   │   ├── ProjectForm.jsx
│   │   │   │   └── ProjectTimeline.jsx
│   │   │   ├── whatsapp/
│   │   │   │   ├── ConversationList.jsx
│   │   │   │   ├── ChatInterface.jsx
│   │   │   │   └── MessageTemplates.jsx
│   │   │   └── reports/
│   │   │       ├── SalesReport.jsx
│   │   │       ├── CustomerReport.jsx
│   │   │       └── ProjectReport.jsx
│   │   ├── pages/
│   │   │   ├── Dashboard.jsx
│   │   │   ├── Products.jsx
│   │   │   ├── Customers.jsx
│   │   │   ├── Projects.jsx
│   │   │   ├── WhatsApp.jsx
│   │   │   ├── Reports.jsx
│   │   │   ├── Settings.jsx
│   │   │   └── Profile.jsx
│   │   ├── hooks/
│   │   │   ├── useAuth.js
│   │   │   ├── useProducts.js
│   │   │   ├── useCustomers.js
│   │   │   ├── useProjects.js
│   │   │   └── useAnalytics.js
│   │   ├── services/
│   │   │   ├── api.js
│   │   │   ├── authService.js
│   │   │   ├── productService.js
│   │   │   ├── customerService.js
│   │   │   ├── projectService.js
│   │   │   └── analyticsService.js
│   │   ├── context/
│   │   │   ├── AuthContext.jsx
│   │   │   ├── DataContext.jsx
│   │   │   └── NotificationContext.jsx
│   │   ├── utils/
│   │   │   ├── constants.js
│   │   │   ├── helpers.js
│   │   │   ├── validators.js
│   │   │   └── permissions.js
│   │   ├── styles/
│   │   │   ├── globals.css
│   │   │   ├── dashboard.css
│   │   │   └── components.css
│   │   ├── assets/
│   │   │   ├── images/
│   │   │   ├── icons/
│   │   │   └── charts/
│   │   ├── App.jsx
│   │   ├── App.css
│   │   └── index.js
│   ├── package.json
│   ├── package-lock.json
│   ├── .env.example
│   ├── .gitignore
│   ├── amplify.yml
│   └── README.md
│
├── shared/                           # Shared utilities and types (optional)
│   ├── types/
│   │   ├── user.js
│   │   ├── product.js
│   │   └── project.js
│   ├── utils/
│   │   ├── validation.js
│   │   └── constants.js
│   └── package.json
│
├── deployment/                       # Deployment configurations
│   ├── aws/
│   │   ├── ec2-setup.sh             # EC2 server setup script
│   │   ├── rds-setup.sql            # PostgreSQL setup
│   │   └── s3-policy.json           # S3 bucket policy
│   ├── docker/
│   │   ├── docker-compose.yml       # Local development
│   │   └── Dockerfile.prod          # Production dockerfile
│   └── scripts/
│       ├── deploy-backend.sh        # Backend deployment
│       ├── deploy-frontend.sh       # Frontend deployment
│       └── database-backup.sh       # Database backup
│
├── docs/                            # Project documentation
│   ├── API.md                       # API documentation
│   ├── DEPLOYMENT.md                # Deployment guide
│   ├── SETUP.md                     # Setup instructions
│   └── ARCHITECTURE.md              # System architecture
│
├── .gitignore                       # Global gitignore
├── README.md                        # Project overview
├── package.json                     # Root package.json for scripts
└── .env.example                     # Environment variables template
```

## Key Features Covered in Schema:

### Customer App Features:
- Product catalog browsing with categories
- User authentication and profiles
- Wishlist functionality
- Consultation request system
- Project tracking
- WhatsApp integration for inquiries

### Internal Portal Features:
- Product management (CRUD operations)
- Customer management and profiles
- Project/consultation management
- WhatsApp conversation management
- Analytics and reporting
- Lead tracking and conversion
- Activity logging

### Technical Considerations:
- **Images**: All product and project images stored in AWS S3
- **WhatsApp Integration**: Business API for customer communication
- **User Roles**: Customer, Admin, Designer, Sales staff
- **Analytics**: Lead tracking, conversion metrics, customer behavior
- **Security**: JWT authentication, role-based access control
- **Performance**: Database indexing for common queries

## Next Steps:
1. Set up the development environment
2. Configure AWS services (EC2, RDS PostgreSQL, S3, Amplify)
3. Implement the backend API with Node.js/Express
4. Build the customer-facing React app
5. Build the internal portal React app
6. Set up WhatsApp Business API integration
7. Configure deployment pipelines

Would you like me to start with any specific part of the implementation, such as the backend API setup or the React components?