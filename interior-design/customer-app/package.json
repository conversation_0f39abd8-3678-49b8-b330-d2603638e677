{"name": "interior-design-customer-app", "version": "1.0.0", "description": "Customer-facing React app for Interior Design Platform", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "@auth0/auth0-react": "^2.2.4", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-helmet-async": "^1.3.0", "framer-motion": "^10.16.16", "swiper": "^11.0.5", "react-intersection-observer": "^9.5.3", "react-infinite-scroll-component": "^6.1.0", "react-image-gallery": "^1.3.0", "react-modal": "^3.16.1", "react-select": "^5.8.0", "react-slick": "^0.29.0", "slick-carousel": "^1.8.1", "classnames": "^2.3.2", "date-fns": "^2.30.0", "lodash": "^4.17.21", "js-cookie": "^3.0.5", "react-icons": "^4.12.0", "tailwindcss": "^3.3.6", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "web-vitals": "^3.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}