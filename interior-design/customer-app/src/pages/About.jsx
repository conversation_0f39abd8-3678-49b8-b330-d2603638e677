// src/pages/About.jsx
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { <PERSON><PERSON>ward, FiUsers, FiHome, FiStar } from 'react-icons/fi';

const About = () => {
  const stats = [
    { icon: FiHome, label: 'Projects Completed', value: '500+' },
    { icon: FiUsers, label: 'Happy Customers', value: '1000+' },
    { icon: FiAward, label: 'Years Experience', value: '10+' },
    { icon: FiStar, label: 'Average Rating', value: '4.9' }
  ];

  const team = [
    {
      name: '<PERSON><PERSON>',
      role: 'Lead Interior Designer',
      experience: '8 years',
      specialization: 'Modern & Contemporary'
    },
    {
      name: '<PERSON><PERSON>',
      role: 'Senior Designer',
      experience: '6 years',
      specialization: 'Traditional & Ethnic'
    },
    {
      name: '<PERSON>',
      role: 'Space Planner',
      experience: '5 years',
      specialization: 'Small Spaces & Apartments'
    }
  ];

  return (
    <>
      <Helmet>
        <title>About Us | Interior Design</title>
        <meta name="description" content="Learn about our interior design company, our team, and our commitment to creating beautiful spaces." />
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-6">About Our Company</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We are passionate interior designers dedicated to transforming spaces into beautiful, 
            functional environments that reflect your personality and lifestyle.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <stat.icon className="w-8 h-8 text-blue-600" />
              </div>
              <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
              <div className="text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Our Story */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Our Story</h2>
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div>
                <p className="text-gray-600 mb-6">
                  Founded in 2014, our interior design company has been transforming homes and 
                  commercial spaces across India. We believe that great design should be accessible 
                  to everyone, which is why we offer personalized solutions for every budget.
                </p>
                <p className="text-gray-600 mb-6">
                  Our team of experienced designers combines creativity with functionality to create 
                  spaces that are not only beautiful but also practical for everyday living.
                </p>
                <p className="text-gray-600">
                  From concept to completion, we work closely with our clients to ensure their 
                  vision becomes reality, delivering exceptional results that exceed expectations.
                </p>
              </div>
              <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                <span className="text-gray-500">Company Image</span>
              </div>
            </div>
          </div>
        </div>

        {/* Our Team */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Meet Our Team</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border p-6 text-center">
                <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4"></div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{member.name}</h3>
                <p className="text-blue-600 font-medium mb-2">{member.role}</p>
                <p className="text-gray-600 text-sm mb-2">{member.experience} experience</p>
                <p className="text-gray-500 text-sm">{member.specialization}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Our Values */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Our Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FiStar className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Quality</h3>
              <p className="text-gray-600">
                We never compromise on quality, using only the finest materials and craftsmanship.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FiUsers className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Collaboration</h3>
              <p className="text-gray-600">
                We work closely with our clients to understand their needs and preferences.
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FiAward className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Innovation</h3>
              <p className="text-gray-600">
                We stay updated with the latest trends and technologies in interior design.
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-blue-50 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Ready to Transform Your Space?</h2>
          <p className="text-gray-600 mb-6">
            Let's work together to create the perfect interior design solution for your home or office.
          </p>
          <a
            href="/consultation"
            className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Book Free Consultation
          </a>
        </div>
      </div>
    </>
  );
};

export default About;
