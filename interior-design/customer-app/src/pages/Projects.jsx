// src/pages/Projects.jsx
import React from 'react';
import { useQuery } from 'react-query';
import { Helmet } from 'react-helmet-async';
import { FiCalendar, FiUser, FiDollarSign, FiClock } from 'react-icons/fi';

import { apiService } from '../services/api';
import LoadingSpinner from '../components/common/LoadingSpinner';

const Projects = () => {
  const { data: projectsData, isLoading, error } = useQuery(
    ['user-projects'],
    () => apiService.projects.getUserProjects()
  );

  const projects = projectsData?.data?.projects || [];

  const formatDate = (date) => {
    if (!date) return 'Not set';
    return new Date(date).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    if (!amount) return 'Not specified';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status) => {
    const colors = {
      inquiry: 'bg-blue-100 text-blue-800',
      consultation_scheduled: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-purple-100 text-purple-800',
      design_ready: 'bg-indigo-100 text-indigo-800',
      approved: 'bg-green-100 text-green-800',
      execution: 'bg-orange-100 text-orange-800',
      completed: 'bg-emerald-100 text-emerald-800',
      cancelled: 'bg-red-100 text-red-800',
      on_hold: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatStatus = (status) => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Projects</h2>
          <p className="text-gray-600">Something went wrong while loading your projects.</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>My Projects | Interior Design</title>
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">My Projects</h1>

        {projects.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
              <FiCalendar className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Projects Yet</h3>
            <p className="text-gray-600 mb-6">You haven't started any projects with us yet.</p>
            <a
              href="/consultation"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Book a Consultation
            </a>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {projects.map((project) => (
              <div key={project.id} className="bg-white rounded-lg shadow-sm border p-6">
                {/* Project Header */}
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-1">
                      {project.project_name}
                    </h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                      {formatStatus(project.status)}
                    </span>
                  </div>
                  <div className="text-sm text-gray-500">
                    #{project.id.slice(0, 8)}
                  </div>
                </div>

                {/* Project Details */}
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <FiCalendar className="w-4 h-4 mr-2" />
                    <span>Created: {formatDate(project.created_at)}</span>
                  </div>

                  {project.assigned_designer && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FiUser className="w-4 h-4 mr-2" />
                      <span>Designer: {project.assigned_designer.first_name} {project.assigned_designer.last_name}</span>
                    </div>
                  )}

                  {(project.budget_min || project.budget_max) && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FiDollarSign className="w-4 h-4 mr-2" />
                      <span>
                        Budget: {formatCurrency(project.budget_min)} 
                        {project.budget_max && ` - ${formatCurrency(project.budget_max)}`}
                      </span>
                    </div>
                  )}

                  {project.timeline && (
                    <div className="flex items-center text-sm text-gray-600">
                      <FiClock className="w-4 h-4 mr-2" />
                      <span>Timeline: {project.timeline}</span>
                    </div>
                  )}
                </div>

                {/* Project Description */}
                {project.description && (
                  <div className="mt-4">
                    <p className="text-sm text-gray-600 line-clamp-3">
                      {project.description}
                    </p>
                  </div>
                )}

                {/* Project Actions */}
                <div className="mt-6 flex space-x-3">
                  <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                    View Details
                  </button>
                  {project.status === 'inquiry' && (
                    <button className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors text-sm">
                      Schedule Call
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default Projects;
