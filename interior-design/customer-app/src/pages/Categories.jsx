import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { FiGrid, FiList, FiSearch } from 'react-icons/fi';

import { categoryService } from '../services/categoryService';
import LoadingSpinner from '../components/common/LoadingSpinner';
import CategoryCard from '../components/catalog/CategoryCard';

const Categories = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'

  // Fetch categories
  const { data: categories, isLoading, error } = useQuery(
    'categories',
    () => categoryService.getCategories(),
    {
      onError: (error) => {
        console.error('Error fetching categories:', error);
      }
    }
  );

  // Filter categories based on search term
  const filteredCategories = categories?.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Group categories by parent
  const parentCategories = filteredCategories.filter(cat => !cat.parent_id);
  const childCategories = filteredCategories.filter(cat => cat.parent_id);

  const getCategoryChildren = (parentId) => {
    return childCategories.filter(cat => cat.parent_id === parentId);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Categories</h2>
          <p className="text-gray-600 mb-6">Something went wrong while loading categories.</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Categories | Interior Design Studio</title>
        <meta name="description" content="Browse our interior design categories including furniture, lighting, decor, and more." />
      </Helmet>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Categories</h1>
          <p className="text-gray-600 max-w-2xl">
            Explore our wide range of interior design categories to find the perfect pieces for your space.
          </p>
        </div>

        {/* Search and View Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-8 space-y-4 sm:space-y-0">
          {/* Search */}
          <div className="relative w-full sm:w-96">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">View:</span>
            <div className="flex border border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                <FiGrid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                <FiList className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Categories Display */}
        {filteredCategories.length === 0 ? (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">No categories found</h3>
            <p className="text-gray-600">
              {searchTerm ? 'Try adjusting your search terms.' : 'No categories available at the moment.'}
            </p>
          </div>
        ) : (
          <div className="space-y-12">
            {/* Parent Categories */}
            {parentCategories.map((category) => {
              const children = getCategoryChildren(category.id);
              
              return (
                <div key={category.id} className="space-y-6">
                  {/* Parent Category */}
                  <div className="border-b border-gray-200 pb-6">
                    <CategoryCard
                      category={category}
                      viewMode={viewMode}
                      onClick={() => navigate(`/categories/${category.slug}`)}
                      isParent={true}
                    />
                  </div>

                  {/* Child Categories */}
                  {children.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {category.name} Subcategories
                      </h3>
                      <div className={
                        viewMode === 'grid'
                          ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                          : 'space-y-4'
                      }>
                        {children.map((childCategory) => (
                          <CategoryCard
                            key={childCategory.id}
                            category={childCategory}
                            viewMode={viewMode}
                            onClick={() => navigate(`/categories/${childCategory.slug}`)}
                            isChild={true}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}

            {/* Standalone Categories (no parent) */}
            {childCategories.filter(cat => !parentCategories.find(parent => parent.id === cat.parent_id)).length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-6">Other Categories</h3>
                <div className={
                  viewMode === 'grid'
                    ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                    : 'space-y-4'
                }>
                  {childCategories
                    .filter(cat => !parentCategories.find(parent => parent.id === cat.parent_id))
                    .map((category) => (
                      <CategoryCard
                        key={category.id}
                        category={category}
                        viewMode={viewMode}
                        onClick={() => navigate(`/categories/${category.slug}`)}
                      />
                    ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Popular Categories Section */}
        {!searchTerm && (
          <div className="mt-16 bg-gray-50 rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              Popular Categories
            </h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
              {[
                { name: 'Living Room', icon: '🛋️', slug: 'living-room' },
                { name: 'Bedroom', icon: '🛏️', slug: 'bedroom' },
                { name: 'Kitchen', icon: '🍳', slug: 'kitchen' },
                { name: 'Dining', icon: '🍽️', slug: 'dining' },
                { name: 'Office', icon: '💼', slug: 'office' },
                { name: 'Lighting', icon: '💡', slug: 'lighting' },
              ].map((item) => (
                <button
                  key={item.slug}
                  onClick={() => navigate(`/categories/${item.slug}`)}
                  className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow"
                >
                  <span className="text-3xl mb-2">{item.icon}</span>
                  <span className="text-sm font-medium text-gray-900">{item.name}</span>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Call to Action */}
        <div className="mt-16 text-center bg-blue-50 rounded-2xl p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Need Help Choosing?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Our design experts can help you find the perfect pieces for your space. 
            Get personalized recommendations based on your style and budget.
          </p>
          <button
            onClick={() => navigate('/consultation')}
            className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Get Free Consultation
          </button>
        </div>
      </div>
    </>
  );
};

export default Categories;
