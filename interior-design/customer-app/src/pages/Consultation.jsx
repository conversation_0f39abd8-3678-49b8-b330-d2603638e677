// src/pages/Consultation.jsx
import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useForm } from 'react-hook-form';
import { useMutation } from 'react-query';
import toast from 'react-hot-toast';
import { FiCalendar, FiClock, FiUser, FiPhone, FiMail, FiHome } from 'react-icons/fi';

import { apiService } from '../services/api';

const Consultation = () => {
  const [selectedService, setSelectedService] = useState('');
  
  const { register, handleSubmit, formState: { errors }, reset } = useForm();

  const consultationMutation = useMutation(
    (data) => apiService.consultations.create(data),
    {
      onSuccess: () => {
        toast.success('Consultation request submitted successfully!');
        reset();
        setSelectedService('');
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || 'Failed to submit consultation request');
      }
    }
  );

  const services = [
    {
      id: 'full_design',
      name: 'Complete Interior Design',
      description: 'Full home interior design with 3D visualization',
      duration: '2-3 hours',
      price: 'Free'
    },
    {
      id: 'room_design',
      name: 'Single Room Design',
      description: 'Design consultation for specific rooms',
      duration: '1-2 hours',
      price: 'Free'
    },
    {
      id: 'consultation',
      name: 'Design Consultation',
      description: 'Expert advice and design recommendations',
      duration: '1 hour',
      price: 'Free'
    },
    {
      id: 'product_selection',
      name: 'Product Selection',
      description: 'Help choosing the right furniture and decor',
      duration: '1 hour',
      price: 'Free'
    }
  ];

  const timeSlots = [
    '09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
    '02:00 PM', '03:00 PM', '04:00 PM', '05:00 PM'
  ];

  const onSubmit = (data) => {
    consultationMutation.mutate({
      ...data,
      service_type: selectedService
    });
  };

  return (
    <>
      <Helmet>
        <title>Book Consultation | Interior Design</title>
        <meta name="description" content="Book a free consultation with our interior design experts" />
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Book Your Free Consultation</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Get expert advice from our interior design professionals. We'll help you transform your space 
            with personalized recommendations and design solutions.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Services */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Choose Your Service</h2>
            <div className="space-y-4">
              {services.map((service) => (
                <div
                  key={service.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedService === service.id
                      ? 'border-blue-600 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedService(service.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-grow">
                      <h3 className="font-medium text-gray-900 mb-1">{service.name}</h3>
                      <p className="text-gray-600 text-sm mb-2">{service.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <FiClock className="w-4 h-4 mr-1" />
                          {service.duration}
                        </div>
                        <div className="flex items-center">
                          <span className="font-medium text-green-600">{service.price}</span>
                        </div>
                      </div>
                    </div>
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      selectedService === service.id
                        ? 'border-blue-600 bg-blue-600'
                        : 'border-gray-300'
                    }`}>
                      {selectedService === service.id && (
                        <div className="w-full h-full rounded-full bg-white scale-50"></div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Booking Form */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Book Your Appointment</h2>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FiUser className="inline w-4 h-4 mr-1" />
                    Full Name *
                  </label>
                  <input
                    type="text"
                    {...register('name', { required: 'Name is required' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Your full name"
                  />
                  {errors.name && (
                    <p className="text-red-600 text-sm mt-1">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FiPhone className="inline w-4 h-4 mr-1" />
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    {...register('phone', { required: 'Phone number is required' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Your phone number"
                  />
                  {errors.phone && (
                    <p className="text-red-600 text-sm mt-1">{errors.phone.message}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FiMail className="inline w-4 h-4 mr-1" />
                  Email Address *
                </label>
                <input
                  type="email"
                  {...register('email', { required: 'Email is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="text-red-600 text-sm mt-1">{errors.email.message}</p>
                )}
              </div>

              {/* Appointment Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FiCalendar className="inline w-4 h-4 mr-1" />
                    Preferred Date *
                  </label>
                  <input
                    type="date"
                    {...register('preferred_date', { required: 'Date is required' })}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  {errors.preferred_date && (
                    <p className="text-red-600 text-sm mt-1">{errors.preferred_date.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <FiClock className="inline w-4 h-4 mr-1" />
                    Preferred Time *
                  </label>
                  <select
                    {...register('preferred_time', { required: 'Time is required' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select time</option>
                    {timeSlots.map((time) => (
                      <option key={time} value={time}>{time}</option>
                    ))}
                  </select>
                  {errors.preferred_time && (
                    <p className="text-red-600 text-sm mt-1">{errors.preferred_time.message}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FiHome className="inline w-4 h-4 mr-1" />
                  Project Details
                </label>
                <textarea
                  {...register('project_details')}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Tell us about your project, space, and design preferences..."
                />
              </div>

              <button
                type="submit"
                disabled={!selectedService || consultationMutation.isLoading}
                className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {consultationMutation.isLoading ? 'Submitting...' : 'Book Consultation'}
              </button>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default Consultation;
