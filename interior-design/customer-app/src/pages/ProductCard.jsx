// src/components/catalog/ProductCard.jsx
import React from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { useWishlist } from "../context/WishlistContext";
import { useCart } from "../context/CartContext";
import { HeartIcon, ShoppingBagIcon, EyeIcon } from "@heroicons/react/24/outline";
import { HeartIcon as HeartSolidIcon } from "@heroicons/react/24/solid";
import { getImageUrl } from "../services/api";

const ProductCard = ({ product, className = "" }) => {
  const { isInWishlist, toggleWishlist } = useWishlist();
  const { addItem, isInCart } = useCart();

  const handleWishlistClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    toggleWishlist(product);
  };

  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();
    addItem(product);
  };

  const getFinalPrice = () => {
    return product.discount_price && product.discount_price < product.price ? product.discount_price : product.price;
  };

  const getDiscountPercentage = () => {
    if (product.discount_price && product.discount_price < product.price) {
      return Math.round(((product.price - product.discount_price) / product.price) * 100);
    }
    return 0;
  };

  const primaryImage = product.images?.find((img) => img.is_primary) || product.images?.[0];
  const imageUrl = primaryImage ? getImageUrl(primaryImage.image_url, "medium") : "/images/placeholder.jpg";

  return (
    <motion.div whileHover={{ y: -5 }} className={`group relative bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden ${className}`}>
      <Link to={`/products/${product.slug}`} className="block">
        {/* Image Container */}
        <div className="relative aspect-square bg-gray-100 overflow-hidden">
          <img src={imageUrl} alt={product.name} className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" loading="lazy" />

          {/* Discount Badge */}
          {getDiscountPercentage() > 0 && <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">{getDiscountPercentage()}% OFF</div>}

          {/* Stock Status */}
          {product.stock_status !== "in_stock" && (
            <div className="absolute top-2 right-2 bg-gray-900 text-white px-2 py-1 rounded text-sm">{product.stock_status === "out_of_stock" ? "Out of Stock" : "On Order"}</div>
          )}

          {/* Quick Actions */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
            <div className="flex space-x-2">
              <button
                onClick={handleWishlistClick}
                className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors duration-200"
                aria-label={isInWishlist(product.id) ? "Remove from wishlist" : "Add to wishlist"}
              >
                {isInWishlist(product.id) ? <HeartSolidIcon className="h-5 w-5 text-red-500" /> : <HeartIcon className="h-5 w-5 text-gray-600" />}
              </button>

              <button
                onClick={handleAddToCart}
                disabled={product.stock_status !== "in_stock"}
                className={`p-2 bg-white rounded-full transition-colors duration-200 ${product.stock_status === "in_stock" ? "hover:bg-gray-100" : "opacity-50 cursor-not-allowed"}`}
                aria-label="Add to cart"
              >
                <ShoppingBagIcon className="h-5 w-5 text-gray-600" />
              </button>

              <Link to={`/products/${product.slug}`} className="p-2 bg-white rounded-full hover:bg-gray-100 transition-colors duration-200" aria-label="View product">
                <EyeIcon className="h-5 w-5 text-gray-600" />
              </Link>
            </div>
          </div>
        </div>

        {/* Product Info */}
        <div className="p-4">
          {/* Category */}
          {product.category && <div className="text-sm text-gray-500 mb-1">{product.category.name}</div>}

          {/* Product Name */}
          <h3 className="font-medium text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors duration-200 line-clamp-2">{product.name}</h3>

          {/* Brand */}
          {product.brand && <div className="text-sm text-gray-600 mb-2">by {product.brand}</div>}

          {/* Price */}
          <div className="flex items-center space-x-2">
            <span className="text-lg font-bold text-gray-900">₹{getFinalPrice()?.toLocaleString()}</span>
            {getDiscountPercentage() > 0 && <span className="text-sm text-gray-500 line-through">₹{product.price?.toLocaleString()}</span>}
          </div>

          {/* Features */}
          <div className="mt-2 flex flex-wrap gap-1">
            {product.room_type && <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">{product.room_type}</span>}
            {product.material && <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">{product.material}</span>}
          </div>

          {/* Add to Cart Button */}
          <button
            onClick={handleAddToCart}
            disabled={product.stock_status !== "in_stock"}
            className={`mt-4 w-full py-2 px-4 rounded-lg font-medium transition-colors duration-200 ${
              product.stock_status === "in_stock"
                ? isInCart(product.id)
                  ? "bg-green-100 text-green-800 border border-green-200"
                  : "bg-indigo-600 text-white hover:bg-indigo-700"
                : "bg-gray-100 text-gray-500 cursor-not-allowed"
            }`}
          >
            {product.stock_status !== "in_stock" ? "Out of Stock" : isInCart(product.id) ? "In Cart" : "Add to Cart"}
          </button>
        </div>
      </Link>
    </motion.div>
  );
};

export default ProductCard;
