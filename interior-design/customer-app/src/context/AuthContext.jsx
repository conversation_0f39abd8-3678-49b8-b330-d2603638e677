// src/context/AuthContext.jsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { apiService } from '../services/api';
import { toast } from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const {
    user: auth0User,
    isAuthenticated,
    isLoading: auth0Loading,
    loginWithRedirect,
    logout: auth0Logout,
    getAccessTokenSilently,
  } = useAuth0();

  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [permissions, setPermissions] = useState([]);

  // Sync user data with backend
  const syncUser = async () => {
    if (!isAuthenticated) {
      setUser(null);
      setPermissions([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Get access token and store it
      const token = await getAccessTokenSilently();
      localStorage.setItem('auth0_token', token);

      // Get user profile from backend
      const response = await apiService.auth.getProfile();
      const userData = response.data.user;
      const userPermissions = response.data.permissions;

      setUser(userData);
      setPermissions(userPermissions);
      
      // Store user data in localStorage for persistence
      localStorage.setItem('user_data', JSON.stringify(userData));
      
    } catch (error) {
      console.error('Failed to sync user:', error);
      toast.error('Failed to sync user data');
      
      // If sync fails, logout user
      if (error.response?.status === 401) {
        await logout();
      }
    } finally {
      setLoading(false);
    }
  };

  // Login function
  const login = async () => {
    await loginWithRedirect();
  };

  // Logout function
  const logout = async () => {
    localStorage.removeItem('auth0_token');
    localStorage.removeItem('user_data');
    setUser(null);
    setPermissions([]);
    
    await auth0Logout({
      logoutParams: {
        returnTo: window.location.origin
      }
    });
  };

  // Update user profile
  const updateProfile = async (profileData) => {
    try {
      const response = await apiService.auth.updateProfile(profileData);
      const updatedUser = response.data.user;
      
      setUser(updatedUser);
      localStorage.setItem('user_data', JSON.stringify(updatedUser));
      
      toast.success('Profile updated successfully');
      return updatedUser;
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw error;
    }
  };

  // Check if user has permission
  const hasPermission = (permission) => {
    return permissions.includes(permission);
  };

  // Check if user is customer
  const isCustomer = () => {
    return user?.user_type === 'customer';
  };

  // Get user's full name
  const getFullName = () => {
    if (!user) return '';
    return `${user.first_name} ${user.last_name || ''}`.trim();
  };

  // Initialize user data on auth state change
  useEffect(() => {
    if (!auth0Loading) {
      syncUser();
    }
  }, [isAuthenticated, auth0Loading]);

  // Restore user data from localStorage on app load
  useEffect(() => {
    const storedUserData = localStorage.getItem('user_data');
    if (storedUserData && isAuthenticated) {
      try {
        const userData = JSON.parse(storedUserData);
        setUser(userData);
      } catch (error) {
        console.error('Failed to parse stored user data:', error);
        localStorage.removeItem('user_data');
      }
    }
  }, []);

  const value = {
    // User data
    user,
    permissions,
    auth0User,
    
    // Loading states
    loading: auth0Loading || loading,
    isAuthenticated,
    
    // Auth methods
    login,
    logout,
    syncUser,
    updateProfile,
    
    // Permission methods
    hasPermission,
    isCustomer,
    
    // Utility methods
    getFullName,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
