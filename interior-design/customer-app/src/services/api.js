// src/services/api.js
import axios from "axios";
import { toast } from "react-hot-toast";

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || "http://localhost:5000/api/v1";

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("auth0_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;

    // Only logout on actual authentication errors, not all 401s
    if (response?.status === 401 && response?.data?.isAuthError) {
      // Unauthorized - redirect to login
      localStorage.removeItem("auth0_token");
      window.location.href = "/login";
      return Promise.reject(error);
    }

    if (response?.status === 403) {
      toast.error("You don't have permission to perform this action");
      return Promise.reject(error);
    }

    if (response?.status >= 500) {
      toast.error("Server error. Please try again later.");
      return Promise.reject(error);
    }

    // Show error message from API if available
    const errorMessage = response?.data?.error || "An error occurred";
    toast.error(errorMessage);

    return Promise.reject(error);
  }
);

// API methods
export const apiService = {
  // Auth methods
  auth: {
    getProfile: () => api.get("/auth/me"),
    updateProfile: (data) => api.put("/auth/profile", data),
    refreshSession: () => api.post("/auth/refresh"),
  },

  // Product methods
  products: {
    getAll: (params = {}) => api.get("/products", { params }),
    getById: (id) => api.get(`/products/${id}`),
    getBySlug: (slug) => api.get(`/products/slug/${slug}`),
    getFeatured: (limit = 10) => api.get("/products/featured", { params: { limit } }),
    getFilters: () => api.get("/products/filters"),
    getRelated: (id, limit = 6) => api.get(`/products/${id}/related`, { params: { limit } }),
    search: (query, filters = {}) =>
      api.get("/products", {
        params: { search: query, ...filters },
      }),
  },

  // Category methods
  categories: {
    getAll: (params = {}) => api.get("/categories", { params }),
    getById: (id) => api.get(`/categories/${id}`),
    getBySlug: (slug) => api.get(`/categories/slug/${slug}`),
    getRoots: () => api.get("/categories/roots"),
    getTree: () => api.get("/categories", { params: { tree: true } }),
  },

  // Wishlist methods
  wishlist: {
    getAll: () => api.get("/wishlist"),
    add: (productId) => api.post(`/wishlist/${productId}`),
    remove: (productId) => api.delete(`/wishlist/${productId}`),
    clear: () => api.delete("/wishlist"),
    check: (productId) => api.get(`/wishlist/check/${productId}`),
    getCount: () => api.get("/wishlist/count"),
  },

  // Project methods
  projects: {
    getAll: () => api.get("/projects"),
    getById: (id) => api.get(`/projects/${id}`),
    create: (data) => api.post("/projects", data),
    update: (id, data) => api.put(`/projects/${id}`, data),
    getMy: () => api.get("/projects/my"),
  },

  // Upload methods
  upload: {
    avatar: (file) => {
      const formData = new FormData();
      formData.append("avatar", file);
      return api.post("/upload/user-avatar", formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
    },
  },

  // Contact/Inquiry methods
  contact: {
    submit: (data) => api.post("/contact", data),
    consultation: (data) => api.post("/consultation", data),
  },
};

// Helper function to handle file uploads
export const uploadFile = async (endpoint, file, progressCallback) => {
  const formData = new FormData();
  formData.append("file", file);

  return api.post(endpoint, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
    onUploadProgress: (progressEvent) => {
      if (progressCallback) {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        progressCallback(percentCompleted);
      }
    },
  });
};

// Helper function to download files
export const downloadFile = async (url, filename) => {
  try {
    const response = await api.get(url, {
      responseType: "blob",
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);

    const link = document.createElement("a");
    link.href = downloadUrl;
    link.download = filename || "download";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    toast.error("Failed to download file");
    throw error;
  }
};

// Helper function to get image URL with size
export const getImageUrl = (url, size = "medium") => {
  if (!url) return "/images/placeholder.jpg";

  if (size === "original") return url;

  // Assuming your S3 service creates different sizes
  const extension = url.split(".").pop();
  const baseUrl = url.replace(`.${extension}`, "");

  return `${baseUrl}_${size}.${extension}`;
};

export default api;
