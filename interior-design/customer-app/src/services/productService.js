import api from './api';

class ProductService {
  // Get all products with filters and pagination
  async getProducts(params = {}) {
    try {
      const response = await api.get('/products', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  }

  // Get single product by ID or slug
  async getProduct(id) {
    try {
      const response = await api.get(`/products/${id}`);
      return response.data.data.product;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw error;
    }
  }

  // Get featured products
  async getFeaturedProducts(limit = 8) {
    try {
      const response = await api.get('/products/featured', {
        params: { limit }
      });
      return response.data.data.products;
    } catch (error) {
      console.error('Error fetching featured products:', error);
      throw error;
    }
  }

  // Get related products
  async getRelatedProducts(productId, limit = 4) {
    try {
      const response = await api.get(`/products/${productId}/related`, {
        params: { limit }
      });
      return response.data.data.products;
    } catch (error) {
      console.error('Error fetching related products:', error);
      throw error;
    }
  }

  // Search products
  async searchProducts(query, filters = {}) {
    try {
      const params = {
        search: query,
        ...filters
      };
      const response = await api.get('/products', { params });
      return response.data;
    } catch (error) {
      console.error('Error searching products:', error);
      throw error;
    }
  }

  // Get products by category
  async getProductsByCategory(categorySlug, params = {}) {
    try {
      const response = await api.get('/products', {
        params: {
          category: categorySlug,
          ...params
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching products by category:', error);
      throw error;
    }
  }

  // Get product reviews
  async getProductReviews(productId, params = {}) {
    try {
      const response = await api.get(`/products/${productId}/reviews`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching product reviews:', error);
      throw error;
    }
  }

  // Add product review
  async addProductReview(productId, reviewData) {
    try {
      const response = await api.post(`/products/${productId}/reviews`, reviewData);
      return response.data;
    } catch (error) {
      console.error('Error adding product review:', error);
      throw error;
    }
  }

  // Get product variants
  async getProductVariants(productId) {
    try {
      const response = await api.get(`/products/${productId}/variants`);
      return response.data.data.variants;
    } catch (error) {
      console.error('Error fetching product variants:', error);
      throw error;
    }
  }

  // Check product availability
  async checkProductAvailability(productId, variantId = null) {
    try {
      const params = variantId ? { variantId } : {};
      const response = await api.get(`/products/${productId}/availability`, { params });
      return response.data.data;
    } catch (error) {
      console.error('Error checking product availability:', error);
      throw error;
    }
  }

  // Get product price
  async getProductPrice(productId, variantId = null, quantity = 1) {
    try {
      const params = { quantity };
      if (variantId) params.variantId = variantId;
      
      const response = await api.get(`/products/${productId}/price`, { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching product price:', error);
      throw error;
    }
  }

  // Get product recommendations
  async getRecommendations(userId = null, limit = 8) {
    try {
      const params = { limit };
      if (userId) params.userId = userId;
      
      const response = await api.get('/products/recommendations', { params });
      return response.data.data.products;
    } catch (error) {
      console.error('Error fetching product recommendations:', error);
      throw error;
    }
  }

  // Track product view
  async trackProductView(productId) {
    try {
      await api.post(`/products/${productId}/view`);
    } catch (error) {
      console.error('Error tracking product view:', error);
      // Don't throw error for analytics tracking
    }
  }

  // Get product filters
  async getProductFilters(categorySlug = null) {
    try {
      const params = categorySlug ? { category: categorySlug } : {};
      const response = await api.get('/products/filters', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching product filters:', error);
      throw error;
    }
  }

  // Get price range for products
  async getPriceRange(categorySlug = null) {
    try {
      const params = categorySlug ? { category: categorySlug } : {};
      const response = await api.get('/products/price-range', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching price range:', error);
      throw error;
    }
  }

  // Get product comparison
  async compareProducts(productIds) {
    try {
      const response = await api.post('/products/compare', { productIds });
      return response.data.data.products;
    } catch (error) {
      console.error('Error comparing products:', error);
      throw error;
    }
  }

  // Get recently viewed products
  async getRecentlyViewed(limit = 8) {
    try {
      const response = await api.get('/products/recently-viewed', {
        params: { limit }
      });
      return response.data.data.products;
    } catch (error) {
      console.error('Error fetching recently viewed products:', error);
      throw error;
    }
  }

  // Get trending products
  async getTrendingProducts(limit = 8) {
    try {
      const response = await api.get('/products/trending', {
        params: { limit }
      });
      return response.data.data.products;
    } catch (error) {
      console.error('Error fetching trending products:', error);
      throw error;
    }
  }

  // Get new arrivals
  async getNewArrivals(limit = 8) {
    try {
      const response = await api.get('/products/new-arrivals', {
        params: { limit }
      });
      return response.data.data.products;
    } catch (error) {
      console.error('Error fetching new arrivals:', error);
      throw error;
    }
  }

  // Get products on sale
  async getSaleProducts(limit = 8) {
    try {
      const response = await api.get('/products/sale', {
        params: { limit }
      });
      return response.data.data.products;
    } catch (error) {
      console.error('Error fetching sale products:', error);
      throw error;
    }
  }

  // Get product by barcode/SKU
  async getProductByCode(code) {
    try {
      const response = await api.get(`/products/code/${code}`);
      return response.data.data.product;
    } catch (error) {
      console.error('Error fetching product by code:', error);
      throw error;
    }
  }

  // Get product stock status
  async getStockStatus(productId, variantId = null) {
    try {
      const params = variantId ? { variantId } : {};
      const response = await api.get(`/products/${productId}/stock`, { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching stock status:', error);
      throw error;
    }
  }

  // Request product notification
  async requestNotification(productId, email, variantId = null) {
    try {
      const data = { email };
      if (variantId) data.variantId = variantId;
      
      const response = await api.post(`/products/${productId}/notify`, data);
      return response.data;
    } catch (error) {
      console.error('Error requesting product notification:', error);
      throw error;
    }
  }

  // Get product size guide
  async getSizeGuide(productId) {
    try {
      const response = await api.get(`/products/${productId}/size-guide`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching size guide:', error);
      throw error;
    }
  }

  // Get product care instructions
  async getCareInstructions(productId) {
    try {
      const response = await api.get(`/products/${productId}/care-instructions`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching care instructions:', error);
      throw error;
    }
  }
}

export const productService = new ProductService();
export default productService;
