import api from './api';

class CategoryService {
  // Get all categories
  async getCategories(params = {}) {
    try {
      const response = await api.get('/categories', { params });
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }

  // Get single category by ID or slug
  async getCategory(id) {
    try {
      const response = await api.get(`/categories/${id}`);
      return response.data.data.category;
    } catch (error) {
      console.error('Error fetching category:', error);
      throw error;
    }
  }

  // Get category tree (hierarchical structure)
  async getCategoryTree() {
    try {
      const response = await api.get('/categories/tree');
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching category tree:', error);
      throw error;
    }
  }

  // Get parent categories only
  async getParentCategories() {
    try {
      const response = await api.get('/categories', {
        params: { parent_only: true }
      });
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching parent categories:', error);
      throw error;
    }
  }

  // Get child categories of a parent
  async getChildCategories(parentId) {
    try {
      const response = await api.get('/categories', {
        params: { parent_id: parentId }
      });
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching child categories:', error);
      throw error;
    }
  }

  // Get featured categories
  async getFeaturedCategories(limit = 6) {
    try {
      const response = await api.get('/categories/featured', {
        params: { limit }
      });
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching featured categories:', error);
      throw error;
    }
  }

  // Get popular categories
  async getPopularCategories(limit = 8) {
    try {
      const response = await api.get('/categories/popular', {
        params: { limit }
      });
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching popular categories:', error);
      throw error;
    }
  }

  // Search categories
  async searchCategories(query) {
    try {
      const response = await api.get('/categories/search', {
        params: { q: query }
      });
      return response.data.data.categories;
    } catch (error) {
      console.error('Error searching categories:', error);
      throw error;
    }
  }

  // Get category breadcrumb
  async getCategoryBreadcrumb(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/breadcrumb`);
      return response.data.data.breadcrumb;
    } catch (error) {
      console.error('Error fetching category breadcrumb:', error);
      throw error;
    }
  }

  // Get category products count
  async getCategoryProductsCount(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/products/count`);
      return response.data.data.count;
    } catch (error) {
      console.error('Error fetching category products count:', error);
      throw error;
    }
  }

  // Get category filters
  async getCategoryFilters(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/filters`);
      return response.data.data.filters;
    } catch (error) {
      console.error('Error fetching category filters:', error);
      throw error;
    }
  }

  // Get category price range
  async getCategoryPriceRange(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/price-range`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching category price range:', error);
      throw error;
    }
  }

  // Get category brands
  async getCategoryBrands(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/brands`);
      return response.data.data.brands;
    } catch (error) {
      console.error('Error fetching category brands:', error);
      throw error;
    }
  }

  // Get category attributes
  async getCategoryAttributes(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/attributes`);
      return response.data.data.attributes;
    } catch (error) {
      console.error('Error fetching category attributes:', error);
      throw error;
    }
  }

  // Get category recommendations
  async getCategoryRecommendations(categoryId, limit = 4) {
    try {
      const response = await api.get(`/categories/${categoryId}/recommendations`, {
        params: { limit }
      });
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching category recommendations:', error);
      throw error;
    }
  }

  // Get trending categories
  async getTrendingCategories(limit = 6) {
    try {
      const response = await api.get('/categories/trending', {
        params: { limit }
      });
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching trending categories:', error);
      throw error;
    }
  }

  // Get new categories
  async getNewCategories(limit = 6) {
    try {
      const response = await api.get('/categories/new', {
        params: { limit }
      });
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching new categories:', error);
      throw error;
    }
  }

  // Get category by path (for nested categories)
  async getCategoryByPath(path) {
    try {
      const response = await api.get(`/categories/path/${encodeURIComponent(path)}`);
      return response.data.data.category;
    } catch (error) {
      console.error('Error fetching category by path:', error);
      throw error;
    }
  }

  // Get category siblings
  async getCategorySiblings(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/siblings`);
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching category siblings:', error);
      throw error;
    }
  }

  // Get category ancestors
  async getCategoryAncestors(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/ancestors`);
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching category ancestors:', error);
      throw error;
    }
  }

  // Get category descendants
  async getCategoryDescendants(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/descendants`);
      return response.data.data.categories;
    } catch (error) {
      console.error('Error fetching category descendants:', error);
      throw error;
    }
  }

  // Track category view
  async trackCategoryView(categoryId) {
    try {
      await api.post(`/categories/${categoryId}/view`);
    } catch (error) {
      console.error('Error tracking category view:', error);
      // Don't throw error for analytics tracking
    }
  }

  // Get category SEO data
  async getCategorySEO(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/seo`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching category SEO data:', error);
      throw error;
    }
  }

  // Get category images
  async getCategoryImages(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/images`);
      return response.data.data.images;
    } catch (error) {
      console.error('Error fetching category images:', error);
      throw error;
    }
  }

  // Get category banner
  async getCategoryBanner(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/banner`);
      return response.data.data.banner;
    } catch (error) {
      console.error('Error fetching category banner:', error);
      throw error;
    }
  }

  // Get category menu structure
  async getCategoryMenu() {
    try {
      const response = await api.get('/categories/menu');
      return response.data.data.menu;
    } catch (error) {
      console.error('Error fetching category menu:', error);
      throw error;
    }
  }

  // Get category statistics
  async getCategoryStats(categoryId) {
    try {
      const response = await api.get(`/categories/${categoryId}/stats`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching category statistics:', error);
      throw error;
    }
  }
}

export const categoryService = new CategoryService();
export default categoryService;
