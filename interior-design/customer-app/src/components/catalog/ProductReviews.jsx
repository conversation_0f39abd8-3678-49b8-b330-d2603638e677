// src/components/catalog/ProductReviews.jsx
import React, { useState } from 'react';
import { useQuery } from 'react-query';
import { FiStar, FiUser, FiThumbsUp } from 'react-icons/fi';

import { apiService } from '../../services/api';
import LoadingSpinner from '../common/LoadingSpinner';

const ProductReviews = ({ product }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const limit = 5;

  const { data: reviewsData, isLoading } = useQuery(
    ['product-reviews', product.id, currentPage],
    () => apiService.products.getReviews(product.id, { page: currentPage, limit }),
    {
      enabled: !!product.id
    }
  );

  const reviews = reviewsData?.data?.reviews || [];
  const pagination = reviewsData?.data?.pagination;
  const averageRating = reviewsData?.data?.averageRating || 0;
  const totalReviews = reviewsData?.data?.totalReviews || 0;

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <FiStar
        key={index}
        className={`w-4 h-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="py-8">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Customer Reviews</h3>
        <div className="flex justify-center">
          <LoadingSpinner size="medium" />
        </div>
      </div>
    );
  }

  return (
    <div className="py-8">
      <h3 className="text-lg font-medium text-gray-900 mb-6">Customer Reviews</h3>

      {/* Rating Summary */}
      {totalReviews > 0 && (
        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">{averageRating.toFixed(1)}</div>
              <div className="flex items-center justify-center space-x-1 mt-1">
                {renderStars(Math.round(averageRating))}
              </div>
              <div className="text-sm text-gray-600 mt-1">
                Based on {totalReviews} review{totalReviews !== 1 ? 's' : ''}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reviews List */}
      {reviews.length > 0 ? (
        <div className="space-y-6">
          {reviews.map((review) => (
            <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                  <FiUser className="w-5 h-5 text-gray-600" />
                </div>
                
                <div className="flex-grow">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {review.customer_name || 'Anonymous'}
                      </h4>
                      <div className="flex items-center space-x-2 mt-1">
                        <div className="flex items-center space-x-1">
                          {renderStars(review.rating)}
                        </div>
                        <span className="text-sm text-gray-600">
                          {formatDate(review.created_at)}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {review.title && (
                    <h5 className="font-medium text-gray-900 mb-2">{review.title}</h5>
                  )}
                  
                  <p className="text-gray-600 leading-relaxed mb-3">{review.comment}</p>
                  
                  {review.verified_purchase && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Verified Purchase
                    </span>
                  )}
                  
                  {review.helpful_count > 0 && (
                    <div className="flex items-center space-x-2 mt-3">
                      <FiThumbsUp className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">
                        {review.helpful_count} people found this helpful
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <FiStar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">No Reviews Yet</h4>
          <p className="text-gray-600">Be the first to review this product!</p>
        </div>
      )}

      {/* Pagination */}
      {pagination && pagination.pages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex space-x-2">
            {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`px-3 py-2 rounded-lg ${
                  page === currentPage
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Write Review Button */}
      <div className="mt-8 text-center">
        <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
          Write a Review
        </button>
      </div>
    </div>
  );
};

export default ProductReviews;
