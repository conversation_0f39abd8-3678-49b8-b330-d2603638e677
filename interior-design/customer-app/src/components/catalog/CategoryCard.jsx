import React from 'react';
import { FiArrowRight, FiPackage } from 'react-icons/fi';

const CategoryCard = ({ 
  category, 
  viewMode = 'grid', 
  onClick, 
  isParent = false, 
  isChild = false 
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick(category);
    }
  };

  const formatProductCount = (count) => {
    if (count === 0) return 'No products';
    if (count === 1) return '1 product';
    return `${count} products`;
  };

  if (viewMode === 'list') {
    return (
      <div
        onClick={handleClick}
        className={`flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200 cursor-pointer group ${
          isParent ? 'border-l-4 border-l-blue-500' : ''
        }`}
      >
        {/* Category Image */}
        <div className="flex-shrink-0 w-16 h-16 mr-4">
          {category.image_url ? (
            <img
              src={category.image_url}
              alt={category.name}
              className="w-full h-full object-cover rounded-lg"
            />
          ) : (
            <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
              <FiPackage className="w-6 h-6 text-gray-400" />
            </div>
          )}
        </div>

        {/* Category Info */}
        <div className="flex-grow">
          <h3 className={`font-medium text-gray-900 group-hover:text-blue-600 transition-colors ${
            isParent ? 'text-lg' : 'text-base'
          }`}>
            {category.name}
          </h3>
          {category.description && (
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
              {category.description}
            </p>
          )}
          <div className="flex items-center mt-2 space-x-4">
            <span className="text-sm text-gray-500">
              {formatProductCount(category.product_count || 0)}
            </span>
            {category.is_featured && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Featured
              </span>
            )}
          </div>
        </div>

        {/* Arrow */}
        <div className="flex-shrink-0 ml-4">
          <FiArrowRight className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
        </div>
      </div>
    );
  }

  // Grid view
  return (
    <div
      onClick={handleClick}
      className={`bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer group ${
        isParent ? 'ring-2 ring-blue-100' : ''
      }`}
    >
      {/* Category Image */}
      <div className={`relative overflow-hidden ${isParent ? 'h-48' : 'h-40'}`}>
        {category.image_url ? (
          <img
            src={category.image_url}
            alt={category.name}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
            <FiPackage className="w-12 h-12 text-gray-400" />
          </div>
        )}
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200" />
        
        {/* Featured Badge */}
        {category.is_featured && (
          <div className="absolute top-3 right-3">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              Featured
            </span>
          </div>
        )}

        {/* Product Count Badge */}
        {category.product_count > 0 && (
          <div className="absolute bottom-3 left-3">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white bg-opacity-90 text-gray-800">
              {formatProductCount(category.product_count)}
            </span>
          </div>
        )}
      </div>

      {/* Category Info */}
      <div className="p-4">
        <h3 className={`font-medium text-gray-900 group-hover:text-blue-600 transition-colors mb-2 ${
          isParent ? 'text-lg' : 'text-base'
        }`}>
          {category.name}
        </h3>
        
        {category.description && (
          <p className="text-sm text-gray-600 line-clamp-2 mb-3">
            {category.description}
          </p>
        )}

        {/* Category Meta */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {isChild && (
              <span className="text-xs text-gray-500">Subcategory</span>
            )}
          </div>
          
          <div className="flex items-center text-blue-600 group-hover:text-blue-700 transition-colors">
            <span className="text-sm font-medium mr-1">Explore</span>
            <FiArrowRight className="w-4 h-4" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryCard;
