// src/components/catalog/ProductSpecifications.jsx
import React from 'react';
import { FiInfo } from 'react-icons/fi';

const ProductSpecifications = ({ product }) => {
  const specifications = [
    { label: 'Brand', value: product.brand },
    { label: 'Material', value: product.material },
    { label: 'Dimensions', value: product.dimensions },
    { label: 'Weight', value: product.weight },
    { label: 'Color', value: product.color },
    { label: 'Room Type', value: product.room_type },
    { label: 'Style', value: product.style },
    { label: 'Care Instructions', value: product.care_instructions },
    { label: 'Warranty', value: product.warranty },
    { label: 'Assembly Required', value: product.assembly_required ? 'Yes' : 'No' }
  ].filter(spec => spec.value); // Only show specs that have values

  if (specifications.length === 0) {
    return (
      <div className="text-center py-8">
        <FiInfo className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">No specifications available for this product.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Product Specifications</h3>
      
      <div className="bg-gray-50 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {specifications.map((spec, index) => (
            <div key={index} className="flex justify-between py-2 border-b border-gray-200 last:border-b-0">
              <span className="font-medium text-gray-700">{spec.label}:</span>
              <span className="text-gray-900">{spec.value}</span>
            </div>
          ))}
        </div>
      </div>

      {product.description && (
        <div className="mt-6">
          <h4 className="text-md font-medium text-gray-900 mb-2">Description</h4>
          <p className="text-gray-600 leading-relaxed">{product.description}</p>
        </div>
      )}

      {product.features && product.features.length > 0 && (
        <div className="mt-6">
          <h4 className="text-md font-medium text-gray-900 mb-2">Key Features</h4>
          <ul className="list-disc list-inside space-y-1 text-gray-600">
            {product.features.map((feature, index) => (
              <li key={index}>{feature}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default ProductSpecifications;
