// src/components/catalog/RelatedProducts.jsx
import React from 'react';
import { useQuery } from 'react-query';
import { FiArrowRight } from 'react-icons/fi';

import { apiService } from '../../services/api';
import ProductCard from '../../pages/ProductCard';
import LoadingSpinner from '../common/LoadingSpinner';

const RelatedProducts = ({ product, limit = 4 }) => {
  const { data: relatedData, isLoading } = useQuery(
    ['related-products', product.id],
    () => apiService.products.getRelated(product.id, { limit }),
    {
      enabled: !!product.id
    }
  );

  const relatedProducts = relatedData?.data?.products || [];

  if (isLoading) {
    return (
      <div className="py-8">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Related Products</h3>
        <div className="flex justify-center">
          <LoadingSpinner size="medium" />
        </div>
      </div>
    );
  }

  if (relatedProducts.length === 0) {
    return null;
  }

  return (
    <div className="py-8">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900">Related Products</h3>
        {relatedProducts.length >= limit && (
          <button className="flex items-center text-blue-600 hover:text-blue-700 font-medium">
            View All
            <FiArrowRight className="w-4 h-4 ml-1" />
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {relatedProducts.map((relatedProduct) => (
          <ProductCard 
            key={relatedProduct.id} 
            product={relatedProduct}
          />
        ))}
      </div>
    </div>
  );
};

export default RelatedProducts;
