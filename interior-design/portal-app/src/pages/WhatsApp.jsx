import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-hot-toast';
import { 
  FiMessageCircle, 
  FiSearch, 
  FiFilter,
  FiUser,
  FiClock,
  FiCheck,
  FiCheckCircle,
  FiAlertCircle
} from 'react-icons/fi';

import { apiService } from '../services/api';
import { usePermissions } from '../hooks/usePermissions';
import LoadingSpinner from '../components/common/LoadingSpinner';
import StatusBadge from '../components/common/StatusBadge';

const WhatsApp = () => {
  const { can } = usePermissions();
  const queryClient = useQueryClient();
  
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    assignedTo: '',
    page: 1,
    limit: 20
  });

  // Fetch conversations
  const { data: conversationsData, isLoading, error } = useQuery(
    ['whatsapp-conversations', filters],
    () => apiService.whatsapp.getConversations(filters),
    {
      keepPreviousData: true,
      refetchInterval: 30000, // Refresh every 30 seconds
      onError: (error) => {
        console.error('Error fetching conversations:', error);
        toast.error('Failed to load conversations');
      }
    }
  );

  // Fetch users for assignment filter
  const { data: usersData } = useQuery(
    'users',
    () => apiService.auth.getUsers({ user_type: ['designer', 'sales', 'admin'] })
  );

  // Mark conversation as read mutation
  const markAsReadMutation = useMutation(
    (conversationId) => apiService.whatsapp.markAsRead(conversationId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('whatsapp-conversations');
      },
      onError: (error) => {
        console.error('Error marking as read:', error);
        toast.error('Failed to mark as read');
      }
    }
  );

  // Update conversation status mutation
  const updateStatusMutation = useMutation(
    ({ conversationId, status }) => apiService.whatsapp.updateStatus(conversationId, status),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('whatsapp-conversations');
        toast.success('Conversation status updated');
      },
      onError: (error) => {
        console.error('Error updating status:', error);
        toast.error('Failed to update status');
      }
    }
  );

  const conversations = conversationsData?.data?.conversations || [];
  const pagination = conversationsData?.data?.pagination || {};
  const users = usersData?.data?.users || [];

  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleMarkAsRead = (conversationId) => {
    markAsReadMutation.mutate(conversationId);
  };

  const handleStatusChange = (conversationId, status) => {
    updateStatusMutation.mutate({ conversationId, status });
  };

  const formatTime = (date) => {
    if (!date) return '';
    const now = new Date();
    const messageDate = new Date(date);
    const diffInHours = (now - messageDate) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString('en-IN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffInHours < 168) { // 7 days
      return messageDate.toLocaleDateString('en-IN', { 
        weekday: 'short',
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return messageDate.toLocaleDateString('en-IN');
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'open':
        return <FiAlertCircle className="w-4 h-4 text-green-500" />;
      case 'in_progress':
        return <FiClock className="w-4 h-4 text-yellow-500" />;
      case 'resolved':
        return <FiCheckCircle className="w-4 h-4 text-blue-500" />;
      case 'closed':
        return <FiCheck className="w-4 h-4 text-gray-500" />;
      default:
        return <FiMessageCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Conversations</h3>
          <p className="text-gray-600 mb-4">Something went wrong while loading WhatsApp conversations.</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>WhatsApp Conversations | Interior Design Portal</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">WhatsApp Conversations</h1>
            <p className="text-gray-600">Manage customer conversations and inquiries</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <FiMessageCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Conversations</p>
                <p className="text-2xl font-bold text-gray-900">{pagination.total || 0}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <FiClock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-gray-900">
                  {conversations.filter(c => c.status === 'in_progress').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FiCheckCircle className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Resolved</p>
                <p className="text-2xl font-bold text-gray-900">
                  {conversations.filter(c => c.status === 'resolved').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <FiAlertCircle className="w-6 h-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Unread</p>
                <p className="text-2xl font-bold text-gray-900">
                  {conversations.filter(c => c.unread_count > 0).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search conversations..."
                value={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Status Filter */}
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Status</option>
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
            </select>

            {/* Assigned To Filter */}
            <select
              value={filters.assignedTo}
              onChange={(e) => handleFilterChange('assignedTo', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Assignees</option>
              <option value="unassigned">Unassigned</option>
              {users.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.first_name} {user.last_name}
                </option>
              ))}
            </select>

            {/* Clear Filters */}
            <button
              onClick={() => setFilters({ search: '', status: '', assignedTo: '', page: 1, limit: 20 })}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Clear Filters
            </button>
          </div>
        </div>

        {/* Conversations List */}
        <div className="bg-white rounded-lg shadow">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <LoadingSpinner size="large" />
            </div>
          ) : conversations.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  className={`p-6 hover:bg-gray-50 transition-colors ${
                    conversation.unread_count > 0 ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 flex-1">
                      {/* Customer Avatar */}
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium">
                          {conversation.customer_name?.split(' ').map(n => n[0]).join('') || 'U'}
                        </span>
                      </div>

                      {/* Conversation Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {conversation.customer_name || conversation.phone_number}
                          </h3>
                          {conversation.unread_count > 0 && (
                            <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                              {conversation.unread_count}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 truncate mt-1">
                          {conversation.last_message || 'No messages yet'}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          <span className="text-xs text-gray-500">
                            {formatTime(conversation.last_message_at)}
                          </span>
                          {conversation.assignedUser && (
                            <span className="text-xs text-gray-500">
                              Assigned to {conversation.assignedUser.first_name}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Status */}
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(conversation.status)}
                        <StatusBadge status={conversation.status} type="whatsapp" />
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      {conversation.unread_count > 0 && (
                        <button
                          onClick={() => handleMarkAsRead(conversation.id)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                          title="Mark as Read"
                        >
                          <FiCheck className="w-4 h-4" />
                        </button>
                      )}

                      {can.manageWhatsApp() && (
                        <select
                          value={conversation.status}
                          onChange={(e) => handleStatusChange(conversation.id, e.target.value)}
                          className="text-sm border border-gray-300 rounded px-2 py-1"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <option value="open">Open</option>
                          <option value="in_progress">In Progress</option>
                          <option value="resolved">Resolved</option>
                          <option value="closed">Closed</option>
                        </select>
                      )}

                      <Link
                        to={`/whatsapp/${conversation.id}`}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg"
                        title="View Conversation"
                      >
                        <FiMessageCircle className="w-4 h-4" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FiMessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Conversations</h3>
              <p className="text-gray-600">No WhatsApp conversations found.</p>
            </div>
          )}

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing {Math.min((pagination.page - 1) * pagination.limit + 1, pagination.total)} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} conversations
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1}
                    className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === pagination.pages}
                    className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50"
                  >
                    Next
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default WhatsApp;
