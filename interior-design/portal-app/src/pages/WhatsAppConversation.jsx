import React, { useState, useEffect, useRef } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-hot-toast';
import { 
  FiArrowLeft, 
  FiSend, 
  FiPaperclip,
  FiUser,
  FiPhone,
  FiMail,
  FiMoreVertical,
  FiCheck,
  FiCheckCircle
} from 'react-icons/fi';

import { apiService } from '../services/api';
import { usePermissions } from '../hooks/usePermissions';
import LoadingSpinner from '../components/common/LoadingSpinner';
import StatusBadge from '../components/common/StatusBadge';

const WhatsAppConversation = () => {
  const { conversationId } = useParams();
  const { can } = usePermissions();
  const queryClient = useQueryClient();
  const messagesEndRef = useRef(null);
  
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  // Fetch conversation details
  const { data: conversationData, isLoading: conversationLoading } = useQuery(
    ['whatsapp-conversation', conversationId],
    () => apiService.whatsapp.getConversation(conversationId),
    {
      refetchInterval: 10000, // Refresh every 10 seconds
    }
  );

  // Fetch messages
  const { data: messagesData, isLoading: messagesLoading } = useQuery(
    ['whatsapp-messages', conversationId],
    () => apiService.whatsapp.getMessages(conversationId),
    {
      refetchInterval: 5000, // Refresh every 5 seconds
      onSuccess: () => {
        scrollToBottom();
      }
    }
  );

  // Send message mutation
  const sendMessageMutation = useMutation(
    (messageData) => apiService.whatsapp.sendMessage(conversationId, messageData),
    {
      onSuccess: () => {
        setMessage('');
        queryClient.invalidateQueries(['whatsapp-messages', conversationId]);
        queryClient.invalidateQueries(['whatsapp-conversation', conversationId]);
        toast.success('Message sent');
      },
      onError: (error) => {
        console.error('Error sending message:', error);
        toast.error('Failed to send message');
      }
    }
  );

  // Update conversation status mutation
  const updateStatusMutation = useMutation(
    (status) => apiService.whatsapp.updateStatus(conversationId, status),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['whatsapp-conversation', conversationId]);
        toast.success('Status updated');
      },
      onError: (error) => {
        console.error('Error updating status:', error);
        toast.error('Failed to update status');
      }
    }
  );

  const conversation = conversationData?.data?.conversation;
  const messages = messagesData?.data?.messages || [];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!message.trim()) return;

    setIsTyping(true);
    try {
      await sendMessageMutation.mutateAsync({
        message: message.trim(),
        type: 'text'
      });
    } finally {
      setIsTyping(false);
    }
  };

  const handleStatusChange = (newStatus) => {
    updateStatusMutation.mutate(newStatus);
  };

  const formatTime = (date) => {
    return new Date(date).toLocaleTimeString('en-IN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatDate = (date) => {
    const messageDate = new Date(date);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (messageDate.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return messageDate.toLocaleDateString('en-IN');
    }
  };

  const getMessageStatus = (message) => {
    if (message.status === 'delivered') {
      return <FiCheckCircle className="w-3 h-3 text-blue-500" />;
    } else if (message.status === 'sent') {
      return <FiCheck className="w-3 h-3 text-gray-400" />;
    }
    return null;
  };

  if (conversationLoading || messagesLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (!conversation) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Conversation Not Found</h3>
          <p className="text-gray-600 mb-4">The conversation you're looking for doesn't exist.</p>
          <Link
            to="/whatsapp"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Conversations
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>WhatsApp - {conversation.customer_name || conversation.phone_number} | Interior Design Portal</title>
      </Helmet>

      <div className="flex flex-col h-full bg-white rounded-lg shadow">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <Link
              to="/whatsapp"
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg"
            >
              <FiArrowLeft className="w-5 h-5" />
            </Link>
            
            <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium">
                {conversation.customer_name?.split(' ').map(n => n[0]).join('') || 'U'}
              </span>
            </div>
            
            <div>
              <h2 className="font-semibold text-gray-900">
                {conversation.customer_name || conversation.phone_number}
              </h2>
              <p className="text-sm text-gray-500">{conversation.phone_number}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <StatusBadge status={conversation.status} type="whatsapp" />
            
            {can.manageWhatsApp() && (
              <select
                value={conversation.status}
                onChange={(e) => handleStatusChange(e.target.value)}
                className="text-sm border border-gray-300 rounded px-2 py-1"
              >
                <option value="open">Open</option>
                <option value="in_progress">In Progress</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
              </select>
            )}

            <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg">
              <FiMoreVertical className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Customer Info */}
        {conversation.customer && (
          <div className="p-4 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <FiUser className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="font-medium text-gray-900">
                    {conversation.customer.first_name} {conversation.customer.last_name}
                  </p>
                  <p className="text-sm text-gray-500">{conversation.customer.email}</p>
                </div>
              </div>
              
              <div className="flex space-x-2">
                <a
                  href={`mailto:${conversation.customer.email}`}
                  className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                >
                  <FiMail className="w-4 h-4" />
                </a>
                <a
                  href={`tel:${conversation.phone_number}`}
                  className="p-2 text-green-600 hover:bg-green-50 rounded-lg"
                >
                  <FiPhone className="w-4 h-4" />
                </a>
              </div>
            </div>
          </div>
        )}

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No messages yet. Start the conversation!</p>
            </div>
          ) : (
            <>
              {messages.map((msg, index) => {
                const showDate = index === 0 || 
                  formatDate(msg.created_at) !== formatDate(messages[index - 1].created_at);
                
                return (
                  <div key={msg.id}>
                    {showDate && (
                      <div className="text-center my-4">
                        <span className="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                          {formatDate(msg.created_at)}
                        </span>
                      </div>
                    )}
                    
                    <div className={`flex ${msg.direction === 'outbound' ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        msg.direction === 'outbound'
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 text-gray-900'
                      }`}>
                        <p className="text-sm">{msg.content}</p>
                        <div className={`flex items-center justify-end space-x-1 mt-1 ${
                          msg.direction === 'outbound' ? 'text-blue-100' : 'text-gray-500'
                        }`}>
                          <span className="text-xs">{formatTime(msg.created_at)}</span>
                          {msg.direction === 'outbound' && getMessageStatus(msg)}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
              <div ref={messagesEndRef} />
            </>
          )}
        </div>

        {/* Message Input */}
        {can.manageWhatsApp() && (
          <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-200">
            <div className="flex items-center space-x-3">
              <button
                type="button"
                className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg"
              >
                <FiPaperclip className="w-5 h-5" />
              </button>
              
              <div className="flex-1">
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type a message..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={isTyping || sendMessageMutation.isLoading}
                />
              </div>
              
              <button
                type="submit"
                disabled={!message.trim() || isTyping || sendMessageMutation.isLoading}
                className="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiSend className="w-5 h-5" />
              </button>
            </div>
          </form>
        )}
      </div>
    </>
  );
};

export default WhatsAppConversation;
