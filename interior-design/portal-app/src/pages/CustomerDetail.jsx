import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import { Helmet } from 'react-helmet-async';
import { 
  FiArrowLeft, 
  FiEdit, 
  FiMail, 
  FiPhone, 
  FiMapPin, 
  FiCalendar,
  FiUser,
  FiMessageCircle,
  FiPlus,
  FiEye
} from 'react-icons/fi';

import { apiService } from '../services/api';
import { usePermissions } from '../hooks/usePermissions';
import LoadingSpinner from '../components/common/LoadingSpinner';
import StatusBadge from '../components/common/StatusBadge';

const CustomerDetail = () => {
  const { id } = useParams();
  const { can } = usePermissions();
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch customer data
  const { data: customerData, isLoading: customerLoading } = useQuery(
    ['customer', id],
    () => apiService.customers.getById(id)
  );

  // Fetch customer projects
  const { data: projectsData, isLoading: projectsLoading } = useQuery(
    ['customer-projects', id],
    () => apiService.customers.getProjects(id)
  );

  const customer = customerData?.data?.customer;
  const projects = projectsData?.data?.projects || [];

  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getCustomerInitials = () => {
    if (!customer) return 'U';
    const first = customer.first_name?.[0] || '';
    const last = customer.last_name?.[0] || '';
    return (first + last).toUpperCase() || 'U';
  };

  const getProjectStatusColor = (status) => {
    const colors = {
      inquiry: 'bg-blue-100 text-blue-800',
      consultation_scheduled: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-purple-100 text-purple-800',
      design_ready: 'bg-indigo-100 text-indigo-800',
      approved: 'bg-green-100 text-green-800',
      execution: 'bg-orange-100 text-orange-800',
      completed: 'bg-emerald-100 text-emerald-800',
      cancelled: 'bg-red-100 text-red-800',
      on_hold: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  if (customerLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Customer Not Found</h3>
          <p className="text-gray-600 mb-4">The customer you're looking for doesn't exist.</p>
          <Link
            to="/customers"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Customers
          </Link>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: FiUser },
    { id: 'projects', name: 'Projects', icon: FiCalendar },
    { id: 'communication', name: 'Communication', icon: FiMessageCircle }
  ];

  return (
    <>
      <Helmet>
        <title>{customer.first_name} {customer.last_name} | Interior Design Portal</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              to="/customers"
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg"
            >
              <FiArrowLeft className="w-5 h-5" />
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {customer.first_name} {customer.last_name}
              </h1>
              <p className="text-gray-600">Customer Details</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <a
              href={`mailto:${customer.email}`}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
              title="Send Email"
            >
              <FiMail className="w-5 h-5" />
            </a>
            
            {customer.phone && (
              <a
                href={`tel:${customer.phone}`}
                className="p-2 text-green-600 hover:bg-green-50 rounded-lg"
                title="Call Customer"
              >
                <FiPhone className="w-5 h-5" />
              </a>
            )}
            
            {can.editCustomers() && (
              <Link
                to={`/customers/${customer.id}/edit`}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
              >
                <FiEdit className="w-4 h-4" />
                <span>Edit</span>
              </Link>
            )}
          </div>
        </div>

        {/* Customer Info Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-start space-x-6">
            {/* Avatar */}
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
              {customer.avatar_url ? (
                <img
                  src={customer.avatar_url}
                  alt={`${customer.first_name} ${customer.last_name}`}
                  className="w-20 h-20 rounded-full object-cover"
                />
              ) : (
                <span className="text-blue-600 font-bold text-2xl">
                  {getCustomerInitials()}
                </span>
              )}
            </div>

            {/* Basic Info */}
            <div className="flex-1">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Contact Information</h3>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <FiMail className="w-4 h-4 text-gray-400 mr-2" />
                      <span>{customer.email}</span>
                    </div>
                    {customer.phone && (
                      <div className="flex items-center text-sm">
                        <FiPhone className="w-4 h-4 text-gray-400 mr-2" />
                        <span>{customer.phone}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Location</h3>
                  <div className="space-y-2">
                    {customer.address && (
                      <div className="flex items-start text-sm">
                        <FiMapPin className="w-4 h-4 text-gray-400 mr-2 mt-0.5" />
                        <div>
                          <div>{customer.address}</div>
                          {(customer.city || customer.state) && (
                            <div>{customer.city}{customer.city && customer.state && ', '}{customer.state}</div>
                          )}
                          {customer.pincode && <div>{customer.pincode}</div>}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Account Status</h3>
                  <div className="space-y-2">
                    <StatusBadge 
                      status={customer.is_active ? 'active' : 'inactive'} 
                      type="user" 
                    />
                    <div className="text-sm text-gray-600">
                      Joined {formatDate(customer.created_at)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FiCalendar className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Projects</p>
                <p className="text-2xl font-bold text-gray-900">{projects.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <FiCalendar className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Projects</p>
                <p className="text-2xl font-bold text-gray-900">
                  {projects.filter(p => ['in_progress', 'design_ready', 'approved', 'execution'].includes(p.status)).length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FiCalendar className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed Projects</p>
                <p className="text-2xl font-bold text-gray-900">
                  {projects.filter(p => p.status === 'completed').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <FiCalendar className="w-6 h-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(
                    projects.reduce((sum, p) => sum + (p.budget_max || 0), 0)
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          <div className="p-6">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Customer Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Full Name</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {customer.first_name} {customer.last_name}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email</label>
                      <p className="mt-1 text-sm text-gray-900">{customer.email}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Phone</label>
                      <p className="mt-1 text-sm text-gray-900">{customer.phone || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Date of Birth</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {customer.date_of_birth ? formatDate(customer.date_of_birth) : 'Not provided'}
                      </p>
                    </div>
                  </div>
                </div>

                {customer.preferences && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Preferences</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(customer.preferences, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Projects Tab */}
            {activeTab === 'projects' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">Projects</h3>
                  {can.createProjects() && (
                    <Link
                      to={`/projects/new?customer=${customer.id}`}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
                    >
                      <FiPlus className="w-4 h-4" />
                      <span>New Project</span>
                    </Link>
                  )}
                </div>

                {projectsLoading ? (
                  <LoadingSpinner />
                ) : projects.length > 0 ? (
                  <div className="space-y-4">
                    {projects.map((project) => (
                      <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{project.project_name}</h4>
                            <p className="text-sm text-gray-600 mt-1">{project.description}</p>
                            <div className="flex items-center space-x-4 mt-2">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getProjectStatusColor(project.status)}`}>
                                {project.status.replace('_', ' ').toUpperCase()}
                              </span>
                              <span className="text-sm text-gray-500">
                                Created {formatDate(project.created_at)}
                              </span>
                              {project.budget_max && (
                                <span className="text-sm text-gray-500">
                                  Budget: {formatCurrency(project.budget_max)}
                                </span>
                              )}
                            </div>
                          </div>
                          <Link
                            to={`/projects/${project.id}`}
                            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
                          >
                            <FiEye className="w-4 h-4" />
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">No projects found for this customer.</p>
                    {can.createProjects() && (
                      <Link
                        to={`/projects/new?customer=${customer.id}`}
                        className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                      >
                        <FiPlus className="w-4 h-4 mr-2" />
                        Create First Project
                      </Link>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Communication Tab */}
            {activeTab === 'communication' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Communication History</h3>
                <div className="text-center py-8">
                  <FiMessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">Communication history will be displayed here.</p>
                  <p className="text-sm text-gray-400 mt-2">
                    This feature will show WhatsApp conversations, emails, and call logs.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default CustomerDetail;
