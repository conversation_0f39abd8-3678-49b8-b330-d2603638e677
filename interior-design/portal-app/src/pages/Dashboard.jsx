import React from 'react';
import { useQuery } from 'react-query';
import { Helmet } from 'react-helmet-async';
import {
  FiUsers,
  FiPackage,
  FiTrendingUp,
  FiMessageCircle,
  FiDollarSign,
  FiShoppingBag,
  FiCalendar,
  FiActivity
} from 'react-icons/fi';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';

import { analyticsService } from '../services/analyticsService';
import LoadingSpinner from '../components/common/LoadingSpinner';
import StatusBadge from '../components/common/StatusBadge';

const Dashboard = () => {
  // Fetch dashboard data
  const { data: dashboardStats, isLoading: statsLoading } = useQuery(
    'dashboardStats',
    () => analyticsService.getDashboardStats(),
    {
      refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
    }
  );

  const { data: recentProjects, isLoading: projectsLoading } = useQuery(
    'recentProjects',
    () => analyticsService.getRecentProjects(5)
  );

  const { data: recentCustomers, isLoading: customersLoading } = useQuery(
    'recentCustomers',
    () => analyticsService.getRecentCustomers(5)
  );

  const { data: salesData, isLoading: salesLoading } = useQuery(
    'salesData',
    () => analyticsService.getSalesData(30)
  );

  if (statsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  const stats = dashboardStats || {
    customers: { total: 0, new: 0, growth: 0 },
    products: { total: 0, active: 0, activePercentage: 0 },
    projects: { total: 0, active: 0, completed: 0, completionRate: 0 },
    conversations: { total: 0, active: 0, responseRate: 0 }
  };

  const statCards = [
    {
      title: 'Total Customers',
      value: stats.customers.total,
      change: `+${stats.customers.new} this month`,
      changeType: 'positive',
      icon: FiUsers,
      color: 'blue'
    },
    {
      title: 'Active Products',
      value: stats.products.active,
      change: `${stats.products.activePercentage}% of total`,
      changeType: 'neutral',
      icon: FiPackage,
      color: 'green'
    },
    {
      title: 'Active Projects',
      value: stats.projects.active,
      change: `${stats.projects.completionRate}% completion rate`,
      changeType: 'positive',
      icon: FiTrendingUp,
      color: 'purple'
    },
    {
      title: 'WhatsApp Chats',
      value: stats.conversations.active,
      change: `${stats.conversations.responseRate}% response rate`,
      changeType: 'positive',
      icon: FiMessageCircle,
      color: 'orange'
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-500 text-blue-600 bg-blue-50',
      green: 'bg-green-500 text-green-600 bg-green-50',
      purple: 'bg-purple-500 text-purple-600 bg-purple-50',
      orange: 'bg-orange-500 text-orange-600 bg-orange-50'
    };
    return colors[color] || colors.blue;
  };

  return (
    <>
      <Helmet>
        <title>Dashboard | Interior Design Portal</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome back! Here's what's happening with your business.</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((stat, index) => {
            const IconComponent = stat.icon;
            const colorClasses = getColorClasses(stat.color).split(' ');

            return (
              <div key={index} className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${colorClasses[2]}`}>
                    <IconComponent className={`w-6 h-6 ${colorClasses[1]}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </div>
                <div className="mt-4">
                  <span className={`text-sm ${
                    stat.changeType === 'positive' ? 'text-green-600' :
                    stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {stat.change}
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sales Chart */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Sales Overview</h3>
            {salesLoading ? (
              <div className="flex items-center justify-center h-64">
                <LoadingSpinner />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={salesData || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="sales" stroke="#3B82F6" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>

          {/* Project Status Chart */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Project Status</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={[
                    { name: 'Active', value: stats.projects.active, fill: '#3B82F6' },
                    { name: 'Completed', value: stats.projects.completed, fill: '#10B981' },
                    { name: 'Pending', value: stats.projects.total - stats.projects.active - stats.projects.completed, fill: '#F59E0B' }
                  ]}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                />
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Projects */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Projects</h3>
            </div>
            <div className="p-6">
              {projectsLoading ? (
                <LoadingSpinner />
              ) : (
                <div className="space-y-4">
                  {(recentProjects || []).map((project, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">{project.project_name}</p>
                        <p className="text-sm text-gray-600">{project.customer_name}</p>
                      </div>
                      <StatusBadge status={project.status} />
                    </div>
                  ))}
                  {(!recentProjects || recentProjects.length === 0) && (
                    <p className="text-gray-500 text-center py-4">No recent projects</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Recent Customers */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Customers</h3>
            </div>
            <div className="p-6">
              {customersLoading ? (
                <LoadingSpinner />
              ) : (
                <div className="space-y-4">
                  {(recentCustomers || []).map((customer, index) => (
                    <div key={index} className="flex items-center">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600">
                          {customer.first_name?.[0]}{customer.last_name?.[0]}
                        </span>
                      </div>
                      <div className="ml-3">
                        <p className="font-medium text-gray-900">
                          {customer.first_name} {customer.last_name}
                        </p>
                        <p className="text-sm text-gray-600">{customer.email}</p>
                      </div>
                    </div>
                  ))}
                  {(!recentCustomers || recentCustomers.length === 0) && (
                    <p className="text-gray-500 text-center py-4">No recent customers</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <FiPackage className="w-8 h-8 text-blue-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Add Product</span>
            </button>
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <FiUsers className="w-8 h-8 text-green-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">New Customer</span>
            </button>
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <FiCalendar className="w-8 h-8 text-purple-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Schedule Meeting</span>
            </button>
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <FiActivity className="w-8 h-8 text-orange-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">View Analytics</span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Dashboard;