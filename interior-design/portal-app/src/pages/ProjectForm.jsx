import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-hot-toast';
import { FiSave, FiX, FiUser, FiCalendar } from 'react-icons/fi';

import { apiService } from '../services/api';
import LoadingSpinner from '../components/common/LoadingSpinner';

const ProjectForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchParams] = useSearchParams();
  const isEdit = Boolean(id);
  const preselectedCustomerId = searchParams.get('customer');

  const [formData, setFormData] = useState({
    projectName: '',
    description: '',
    projectType: 'consultation',
    customerId: preselectedCustomerId || '',
    budgetMin: '',
    budgetMax: '',
    consultationDate: '',
    expectedCompletion: '',
    requirements: '',
    notes: '',
    status: 'inquiry',
    assignedTo: ''
  });

  const [loading, setLoading] = useState(false);

  // Fetch project data for editing
  const { data: project, isLoading: projectLoading } = useQuery(
    ['project', id],
    () => apiService.projects.getById(id),
    {
      enabled: isEdit,
      onSuccess: (data) => {
        const project = data.data.project;
        setFormData({
          projectName: project.project_name || '',
          description: project.description || '',
          projectType: project.project_type || 'consultation',
          customerId: project.customer_id || '',
          budgetMin: project.budget_min || '',
          budgetMax: project.budget_max || '',
          consultationDate: project.consultation_date ? 
            new Date(project.consultation_date).toISOString().split('T')[0] : '',
          expectedCompletion: project.expected_completion ? 
            new Date(project.expected_completion).toISOString().split('T')[0] : '',
          requirements: typeof project.requirements === 'string' 
            ? project.requirements 
            : JSON.stringify(project.requirements, null, 2) || '',
          notes: project.notes || '',
          status: project.status || 'inquiry',
          assignedTo: project.assigned_to || ''
        });
      }
    }
  );

  // Fetch customers
  const { data: customersData } = useQuery(
    'customers',
    () => apiService.customers.getAll({ limit: 100 })
  );

  // Fetch users for assignment
  const { data: usersData } = useQuery(
    'users',
    () => apiService.auth.getUsers({ user_type: ['designer', 'sales'] })
  );

  // Create/Update project mutation
  const saveProjectMutation = useMutation(
    (projectData) => {
      if (isEdit) {
        return apiService.projects.update(id, projectData);
      } else {
        return apiService.projects.create(projectData);
      }
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('projects');
        toast.success(isEdit ? 'Project updated successfully' : 'Project created successfully');
        navigate('/projects');
      },
      onError: (error) => {
        console.error('Error saving project:', error);
        toast.error('Failed to save project');
      }
    }
  );

  const customers = customersData?.data?.customers || [];
  const users = usersData?.data?.users || [];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const projectData = {
        project_name: formData.projectName,
        description: formData.description,
        project_type: formData.projectType,
        customer_id: formData.customerId,
        budget_min: formData.budgetMin ? parseFloat(formData.budgetMin) : null,
        budget_max: formData.budgetMax ? parseFloat(formData.budgetMax) : null,
        consultation_date: formData.consultationDate || null,
        expected_completion: formData.expectedCompletion || null,
        requirements: formData.requirements,
        notes: formData.notes,
        status: formData.status,
        assigned_to: formData.assignedTo || null
      };

      await saveProjectMutation.mutateAsync(projectData);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setLoading(false);
    }
  };

  if (isEdit && projectLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{isEdit ? 'Edit Project' : 'New Project'} | Interior Design Portal</title>
      </Helmet>

      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEdit ? 'Edit Project' : 'Create New Project'}
            </h1>
            <p className="text-gray-600">
              {isEdit ? 'Update project information' : 'Start a new interior design project'}
            </p>
          </div>
          <button
            onClick={() => navigate('/projects')}
            className="text-gray-600 hover:text-gray-800"
          >
            <FiX className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Project Name *
                </label>
                <input
                  type="text"
                  name="projectName"
                  value={formData.projectName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Customer *
                </label>
                <select
                  name="customerId"
                  value={formData.customerId}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select Customer</option>
                  {customers.map((customer) => (
                    <option key={customer.id} value={customer.id}>
                      {customer.first_name} {customer.last_name} - {customer.email}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Project Type *
                </label>
                <select
                  name="projectType"
                  value={formData.projectType}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="consultation">Consultation</option>
                  <option value="full_design">Full Design</option>
                  <option value="partial_design">Partial Design</option>
                  <option value="product_only">Product Only</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="inquiry">Inquiry</option>
                  <option value="consultation_scheduled">Consultation Scheduled</option>
                  <option value="in_progress">In Progress</option>
                  <option value="design_ready">Design Ready</option>
                  <option value="approved">Approved</option>
                  <option value="execution">Execution</option>
                  <option value="completed">Completed</option>
                  <option value="on_hold">On Hold</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assigned To
                </label>
                <select
                  name="assignedTo"
                  value={formData.assignedTo}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Unassigned</option>
                  {users.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.first_name} {user.last_name} ({user.user_type})
                    </option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Budget */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Budget</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Minimum Budget (₹)
                </label>
                <input
                  type="number"
                  name="budgetMin"
                  value={formData.budgetMin}
                  onChange={handleInputChange}
                  min="0"
                  step="1000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Budget (₹)
                </label>
                <input
                  type="number"
                  name="budgetMax"
                  value={formData.budgetMax}
                  onChange={handleInputChange}
                  min="0"
                  step="1000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Timeline</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Consultation Date
                </label>
                <input
                  type="date"
                  name="consultationDate"
                  value={formData.consultationDate}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Expected Completion
                </label>
                <input
                  type="date"
                  name="expectedCompletion"
                  value={formData.expectedCompletion}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Requirements & Notes */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Requirements & Notes</h2>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Requirements
                </label>
                <textarea
                  name="requirements"
                  value={formData.requirements}
                  onChange={handleInputChange}
                  rows={6}
                  placeholder="Describe the project requirements, room details, style preferences, etc."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Internal Notes
                </label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={4}
                  placeholder="Internal notes for team members..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => navigate('/projects')}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || saveProjectMutation.isLoading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
            >
              <FiSave className="w-4 h-4" />
              <span>
                {loading || saveProjectMutation.isLoading 
                  ? 'Saving...' 
                  : isEdit ? 'Update Project' : 'Create Project'
                }
              </span>
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

export default ProjectForm;
