import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-hot-toast';
import { 
  FiPlus, 
  FiEdit, 
  FiEye, 
  FiSearch, 
  FiFilter,
  FiCalendar,
  FiUser,
  FiDollarSign,
  FiClock
} from 'react-icons/fi';

import { apiService } from '../services/api';
import { usePermissions } from '../hooks/usePermissions';
import LoadingSpinner from '../components/common/LoadingSpinner';
import DataTable from '../components/common/DataTable';
import StatusBadge from '../components/common/StatusBadge';

const Projects = () => {
  const { can } = usePermissions();
  const queryClient = useQueryClient();
  
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    type: '',
    assignedTo: '',
    page: 1,
    limit: 20
  });

  // Fetch projects
  const { data: projectsData, isLoading, error } = useQuery(
    ['projects', filters],
    () => apiService.projects.getAll(filters),
    {
      keepPreviousData: true,
      onError: (error) => {
        console.error('Error fetching projects:', error);
        toast.error('Failed to load projects');
      }
    }
  );

  // Fetch users for assignment filter
  const { data: usersData } = useQuery(
    'users',
    () => apiService.auth.getUsers({ user_type: ['designer', 'sales'] })
  );

  // Update project status mutation
  const updateStatusMutation = useMutation(
    ({ projectId, status }) => apiService.projects.updateStatus(projectId, status),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('projects');
        toast.success('Project status updated');
      },
      onError: (error) => {
        console.error('Error updating project status:', error);
        toast.error('Failed to update project status');
      }
    }
  );

  const projects = projectsData?.data?.projects || [];
  const pagination = projectsData?.data?.pagination || {};
  const users = usersData?.data?.users || [];

  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleStatusChange = (projectId, newStatus) => {
    updateStatusMutation.mutate({ projectId, status: newStatus });
  };

  const formatCurrency = (amount) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-IN');
  };

  const getStatusColor = (status) => {
    const colors = {
      inquiry: 'bg-blue-100 text-blue-800',
      consultation_scheduled: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-purple-100 text-purple-800',
      design_ready: 'bg-indigo-100 text-indigo-800',
      approved: 'bg-green-100 text-green-800',
      execution: 'bg-orange-100 text-orange-800',
      completed: 'bg-emerald-100 text-emerald-800',
      cancelled: 'bg-red-100 text-red-800',
      on_hold: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getProjectTypeIcon = (type) => {
    const icons = {
      consultation: '💬',
      full_design: '🏠',
      partial_design: '🛋️',
      product_only: '📦'
    };
    return icons[type] || '📋';
  };

  const columns = [
    {
      header: 'Project',
      accessor: 'project_name',
      cell: (project) => (
        <div className="flex items-center">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
            <span className="text-lg">{getProjectTypeIcon(project.project_type)}</span>
          </div>
          <div>
            <div className="font-medium text-gray-900">{project.project_name}</div>
            <div className="text-sm text-gray-500 capitalize">
              {project.project_type?.replace('_', ' ')}
            </div>
          </div>
        </div>
      )
    },
    {
      header: 'Customer',
      accessor: 'customer',
      cell: (project) => (
        <div>
          <div className="font-medium text-gray-900">
            {project.customer?.first_name} {project.customer?.last_name}
          </div>
          <div className="text-sm text-gray-500">{project.customer?.email}</div>
        </div>
      )
    },
    {
      header: 'Status',
      accessor: 'status',
      cell: (project) => (
        <select
          value={project.status}
          onChange={(e) => handleStatusChange(project.id, e.target.value)}
          className={`px-2 py-1 rounded-full text-xs font-medium border-0 ${getStatusColor(project.status)}`}
          disabled={!can.editProjects()}
        >
          <option value="inquiry">Inquiry</option>
          <option value="consultation_scheduled">Consultation Scheduled</option>
          <option value="in_progress">In Progress</option>
          <option value="design_ready">Design Ready</option>
          <option value="approved">Approved</option>
          <option value="execution">Execution</option>
          <option value="completed">Completed</option>
          <option value="on_hold">On Hold</option>
          <option value="cancelled">Cancelled</option>
        </select>
      )
    },
    {
      header: 'Budget',
      accessor: 'budget',
      cell: (project) => (
        <div>
          {project.budget_min && project.budget_max ? (
            <div className="text-sm">
              <div>{formatCurrency(project.budget_min)}</div>
              <div className="text-gray-500">to {formatCurrency(project.budget_max)}</div>
            </div>
          ) : (
            <span className="text-gray-500">Not specified</span>
          )}
        </div>
      )
    },
    {
      header: 'Assigned To',
      accessor: 'assigned_to',
      cell: (project) => (
        <div className="flex items-center">
          {project.assignedUser ? (
            <>
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-2">
                <span className="text-xs font-medium">
                  {project.assignedUser.first_name?.[0]}{project.assignedUser.last_name?.[0]}
                </span>
              </div>
              <div>
                <div className="text-sm font-medium">
                  {project.assignedUser.first_name} {project.assignedUser.last_name}
                </div>
                <div className="text-xs text-gray-500 capitalize">
                  {project.assignedUser.user_type}
                </div>
              </div>
            </>
          ) : (
            <span className="text-gray-500 text-sm">Unassigned</span>
          )}
        </div>
      )
    },
    {
      header: 'Created',
      accessor: 'created_at',
      cell: (project) => (
        <div className="text-sm text-gray-600">
          {formatDate(project.created_at)}
        </div>
      )
    },
    {
      header: 'Actions',
      accessor: 'actions',
      cell: (project) => (
        <div className="flex items-center space-x-2">
          <Link
            to={`/projects/${project.id}`}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
            title="View Project"
          >
            <FiEye className="w-4 h-4" />
          </Link>
          
          {can.editProjects() && (
            <Link
              to={`/projects/${project.id}/edit`}
              className="p-2 text-green-600 hover:bg-green-50 rounded-lg"
              title="Edit Project"
            >
              <FiEdit className="w-4 h-4" />
            </Link>
          )}
        </div>
      )
    }
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Projects</h3>
          <p className="text-gray-600 mb-4">Something went wrong while loading projects.</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Projects | Interior Design Portal</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
            <p className="text-gray-600">Manage customer projects and consultations</p>
          </div>
          
          {can.createProjects() && (
            <Link
              to="/projects/new"
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <FiPlus className="w-4 h-4" />
              <span>New Project</span>
            </Link>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FiCalendar className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Projects</p>
                <p className="text-2xl font-bold text-gray-900">{pagination.total || 0}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <FiClock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">In Progress</p>
                <p className="text-2xl font-bold text-gray-900">
                  {projects.filter(p => p.status === 'in_progress').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <FiUser className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {projects.filter(p => p.status === 'completed').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FiDollarSign className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Budget</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(
                    projects.reduce((sum, p) => sum + (p.budget_max || 0), 0) / projects.length || 0
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search projects..."
                value={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Status Filter */}
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Status</option>
              <option value="inquiry">Inquiry</option>
              <option value="consultation_scheduled">Consultation Scheduled</option>
              <option value="in_progress">In Progress</option>
              <option value="design_ready">Design Ready</option>
              <option value="approved">Approved</option>
              <option value="execution">Execution</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="on_hold">On Hold</option>
            </select>

            {/* Type Filter */}
            <select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Types</option>
              <option value="consultation">Consultation</option>
              <option value="full_design">Full Design</option>
              <option value="partial_design">Partial Design</option>
              <option value="product_only">Product Only</option>
            </select>

            {/* Assigned To Filter */}
            <select
              value={filters.assignedTo}
              onChange={(e) => handleFilterChange('assignedTo', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Assignees</option>
              <option value="unassigned">Unassigned</option>
              {users.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.first_name} {user.last_name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Projects Table */}
        <div className="bg-white rounded-lg shadow">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <LoadingSpinner size="large" />
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={projects}
              pagination={pagination}
              onPageChange={handlePageChange}
              emptyMessage="No projects found"
            />
          )}
        </div>
      </div>
    </>
  );
};

export default Projects;
