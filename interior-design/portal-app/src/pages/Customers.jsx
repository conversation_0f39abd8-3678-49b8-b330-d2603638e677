import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-hot-toast';
import { 
  FiPlus, 
  FiEdit, 
  FiEye, 
  FiSearch, 
  FiFilter,
  FiMail,
  FiPhone,
  FiMapPin,
  FiCalendar,
  FiUser
} from 'react-icons/fi';

import { apiService } from '../services/api';
import { usePermissions } from '../hooks/usePermissions';
import LoadingSpinner from '../components/common/LoadingSpinner';
import DataTable from '../components/common/DataTable';
import StatusBadge from '../components/common/StatusBadge';

const Customers = () => {
  const { can } = usePermissions();
  const queryClient = useQueryClient();
  
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    location: '',
    page: 1,
    limit: 20
  });

  // Fetch customers
  const { data: customersData, isLoading, error } = useQuery(
    ['customers', filters],
    () => apiService.customers.getAll(filters),
    {
      keepPreviousData: true,
      onError: (error) => {
        console.error('Error fetching customers:', error);
        toast.error('Failed to load customers');
      }
    }
  );

  const customers = customersData?.data?.customers || [];
  const pagination = customersData?.data?.pagination || {};

  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-IN');
  };

  const getCustomerInitials = (customer) => {
    const first = customer.first_name?.[0] || '';
    const last = customer.last_name?.[0] || '';
    return (first + last).toUpperCase() || 'U';
  };

  const columns = [
    {
      header: 'Customer',
      accessor: 'name',
      cell: (customer) => (
        <div className="flex items-center">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
            {customer.avatar_url ? (
              <img
                src={customer.avatar_url}
                alt={`${customer.first_name} ${customer.last_name}`}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <span className="text-blue-600 font-medium text-sm">
                {getCustomerInitials(customer)}
              </span>
            )}
          </div>
          <div>
            <div className="font-medium text-gray-900">
              {customer.first_name} {customer.last_name}
            </div>
            <div className="text-sm text-gray-500">{customer.email}</div>
          </div>
        </div>
      )
    },
    {
      header: 'Contact',
      accessor: 'contact',
      cell: (customer) => (
        <div className="space-y-1">
          {customer.phone && (
            <div className="flex items-center text-sm text-gray-600">
              <FiPhone className="w-3 h-3 mr-1" />
              {customer.phone}
            </div>
          )}
          {customer.email && (
            <div className="flex items-center text-sm text-gray-600">
              <FiMail className="w-3 h-3 mr-1" />
              {customer.email}
            </div>
          )}
        </div>
      )
    },
    {
      header: 'Location',
      accessor: 'location',
      cell: (customer) => (
        <div className="text-sm text-gray-600">
          {customer.city && customer.state ? (
            <div className="flex items-center">
              <FiMapPin className="w-3 h-3 mr-1" />
              {customer.city}, {customer.state}
            </div>
          ) : (
            'Not specified'
          )}
        </div>
      )
    },
    {
      header: 'Projects',
      accessor: 'project_count',
      cell: (customer) => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {customer.project_count || 0} projects
          </div>
          <div className="text-gray-500">
            {customer.active_projects || 0} active
          </div>
        </div>
      )
    },
    {
      header: 'Last Activity',
      accessor: 'last_activity',
      cell: (customer) => (
        <div className="text-sm text-gray-600">
          {customer.last_activity ? (
            <div className="flex items-center">
              <FiCalendar className="w-3 h-3 mr-1" />
              {formatDate(customer.last_activity)}
            </div>
          ) : (
            'No activity'
          )}
        </div>
      )
    },
    {
      header: 'Status',
      accessor: 'status',
      cell: (customer) => (
        <StatusBadge 
          status={customer.is_active ? 'active' : 'inactive'} 
          type="user" 
        />
      )
    },
    {
      header: 'Actions',
      accessor: 'actions',
      cell: (customer) => (
        <div className="flex items-center space-x-2">
          <Link
            to={`/customers/${customer.id}`}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg"
            title="View Customer"
          >
            <FiEye className="w-4 h-4" />
          </Link>
          
          {can.editCustomers() && (
            <Link
              to={`/customers/${customer.id}/edit`}
              className="p-2 text-green-600 hover:bg-green-50 rounded-lg"
              title="Edit Customer"
            >
              <FiEdit className="w-4 h-4" />
            </Link>
          )}
          
          <a
            href={`mailto:${customer.email}`}
            className="p-2 text-purple-600 hover:bg-purple-50 rounded-lg"
            title="Send Email"
          >
            <FiMail className="w-4 h-4" />
          </a>
          
          {customer.phone && (
            <a
              href={`tel:${customer.phone}`}
              className="p-2 text-orange-600 hover:bg-orange-50 rounded-lg"
              title="Call Customer"
            >
              <FiPhone className="w-4 h-4" />
            </a>
          )}
        </div>
      )
    }
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Customers</h3>
          <p className="text-gray-600 mb-4">Something went wrong while loading customers.</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Customers | Interior Design Portal</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Customers</h1>
            <p className="text-gray-600">Manage your customer relationships</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FiUser className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Customers</p>
                <p className="text-2xl font-bold text-gray-900">{pagination.total || 0}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <FiUser className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Customers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {customers.filter(c => c.is_active).length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FiCalendar className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">New This Month</p>
                <p className="text-2xl font-bold text-gray-900">
                  {customers.filter(c => {
                    const created = new Date(c.created_at);
                    const now = new Date();
                    return created.getMonth() === now.getMonth() && 
                           created.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <FiMapPin className="w-6 h-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Cities</p>
                <p className="text-2xl font-bold text-gray-900">
                  {new Set(customers.map(c => c.city).filter(Boolean)).size}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search customers..."
                value={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Status Filter */}
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>

            {/* Location Filter */}
            <select
              value={filters.location}
              onChange={(e) => handleFilterChange('location', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Locations</option>
              {Array.from(new Set(customers.map(c => c.city).filter(Boolean))).map(city => (
                <option key={city} value={city}>{city}</option>
              ))}
            </select>

            {/* Clear Filters */}
            <button
              onClick={() => setFilters({ search: '', status: '', location: '', page: 1, limit: 20 })}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Clear Filters
            </button>
          </div>
        </div>

        {/* Customers Table */}
        <div className="bg-white rounded-lg shadow">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <LoadingSpinner size="large" />
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={customers}
              pagination={pagination}
              onPageChange={handlePageChange}
              emptyMessage="No customers found"
            />
          )}
        </div>
      </div>
    </>
  );
};

export default Customers;
