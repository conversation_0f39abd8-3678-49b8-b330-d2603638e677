import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Helmet } from 'react-helmet-async';
import { toast } from 'react-hot-toast';
import { 
  FiArrowLeft, 
  FiEdit, 
  FiUser, 
  FiCalendar, 
  FiDollarSign,
  FiMapPin,
  FiClock,
  FiFileText,
  FiImage,
  FiMessageCircle,
  FiPhone,
  FiMail
} from 'react-icons/fi';

import { apiService } from '../services/api';
import { usePermissions } from '../hooks/usePermissions';
import LoadingSpinner from '../components/common/LoadingSpinner';
import StatusBadge from '../components/common/StatusBadge';

const ProjectDetail = () => {
  const { id } = useParams();
  const { can } = usePermissions();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch project data
  const { data: projectData, isLoading: projectLoading } = useQuery(
    ['project', id],
    () => apiService.projects.getById(id)
  );

  // Fetch project files
  const { data: filesData, isLoading: filesLoading } = useQuery(
    ['project-files', id],
    () => apiService.projects.getFiles(id)
  );

  // Update project status mutation
  const updateStatusMutation = useMutation(
    (status) => apiService.projects.updateStatus(id, status),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['project', id]);
        toast.success('Project status updated');
      },
      onError: (error) => {
        console.error('Error updating project status:', error);
        toast.error('Failed to update project status');
      }
    }
  );

  const project = projectData?.data?.project;
  const files = filesData?.data?.files || [];

  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status) => {
    const colors = {
      inquiry: 'bg-blue-100 text-blue-800',
      consultation_scheduled: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-purple-100 text-purple-800',
      design_ready: 'bg-indigo-100 text-indigo-800',
      approved: 'bg-green-100 text-green-800',
      execution: 'bg-orange-100 text-orange-800',
      completed: 'bg-emerald-100 text-emerald-800',
      cancelled: 'bg-red-100 text-red-800',
      on_hold: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getProjectTypeIcon = (type) => {
    const icons = {
      consultation: '💬',
      full_design: '🏠',
      partial_design: '🛋️',
      product_only: '📦'
    };
    return icons[type] || '📋';
  };

  const handleStatusChange = (newStatus) => {
    updateStatusMutation.mutate(newStatus);
  };

  if (projectLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Project Not Found</h3>
          <p className="text-gray-600 mb-4">The project you're looking for doesn't exist.</p>
          <Link
            to="/projects"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Projects
          </Link>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: FiFileText },
    { id: 'files', name: 'Files & Documents', icon: FiImage },
    { id: 'timeline', name: 'Timeline', icon: FiClock },
    { id: 'communication', name: 'Communication', icon: FiMessageCircle }
  ];

  return (
    <>
      <Helmet>
        <title>{project.project_name} | Interior Design Portal</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              to="/projects"
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg"
            >
              <FiArrowLeft className="w-5 h-5" />
            </Link>
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">{getProjectTypeIcon(project.project_type)}</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{project.project_name}</h1>
                <p className="text-gray-600 capitalize">
                  {project.project_type?.replace('_', ' ')} Project
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {can.editProjects() && (
              <Link
                to={`/projects/${project.id}/edit`}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
              >
                <FiEdit className="w-4 h-4" />
                <span>Edit</span>
              </Link>
            )}
          </div>
        </div>

        {/* Project Info Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Customer Info */}
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Customer</h3>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium">
                    {project.customer?.first_name?.[0]}{project.customer?.last_name?.[0]}
                  </span>
                </div>
                <div>
                  <div className="font-medium text-gray-900">
                    {project.customer?.first_name} {project.customer?.last_name}
                  </div>
                  <div className="text-sm text-gray-500">{project.customer?.email}</div>
                </div>
              </div>
              <div className="mt-3 flex space-x-2">
                <a
                  href={`mailto:${project.customer?.email}`}
                  className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                >
                  <FiMail className="w-4 h-4" />
                </a>
                {project.customer?.phone && (
                  <a
                    href={`tel:${project.customer?.phone}`}
                    className="p-1 text-green-600 hover:bg-green-50 rounded"
                  >
                    <FiPhone className="w-4 h-4" />
                  </a>
                )}
              </div>
            </div>

            {/* Status */}
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Status</h3>
              {can.editProjects() ? (
                <select
                  value={project.status}
                  onChange={(e) => handleStatusChange(e.target.value)}
                  className={`px-3 py-1 rounded-full text-sm font-medium border-0 ${getStatusColor(project.status)}`}
                >
                  <option value="inquiry">Inquiry</option>
                  <option value="consultation_scheduled">Consultation Scheduled</option>
                  <option value="in_progress">In Progress</option>
                  <option value="design_ready">Design Ready</option>
                  <option value="approved">Approved</option>
                  <option value="execution">Execution</option>
                  <option value="completed">Completed</option>
                  <option value="on_hold">On Hold</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              ) : (
                <StatusBadge status={project.status} type="project" />
              )}
            </div>

            {/* Budget */}
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Budget</h3>
              <div>
                {project.budget_min && project.budget_max ? (
                  <div>
                    <div className="font-medium text-gray-900">
                      {formatCurrency(project.budget_min)} - {formatCurrency(project.budget_max)}
                    </div>
                  </div>
                ) : (
                  <span className="text-gray-500">Not specified</span>
                )}
              </div>
            </div>

            {/* Timeline */}
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-2">Timeline</h3>
              <div className="space-y-1">
                <div className="text-sm">
                  <span className="text-gray-600">Created:</span>
                  <span className="ml-1 font-medium">{formatDate(project.created_at)}</span>
                </div>
                {project.consultation_date && (
                  <div className="text-sm">
                    <span className="text-gray-600">Consultation:</span>
                    <span className="ml-1 font-medium">{formatDate(project.consultation_date)}</span>
                  </div>
                )}
                {project.expected_completion && (
                  <div className="text-sm">
                    <span className="text-gray-600">Expected:</span>
                    <span className="ml-1 font-medium">{formatDate(project.expected_completion)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Assigned Team */}
        {project.assignedUser && (
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Assigned Team</h3>
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                <span className="font-medium">
                  {project.assignedUser.first_name?.[0]}{project.assignedUser.last_name?.[0]}
                </span>
              </div>
              <div>
                <div className="font-medium text-gray-900">
                  {project.assignedUser.first_name} {project.assignedUser.last_name}
                </div>
                <div className="text-sm text-gray-500 capitalize">
                  {project.assignedUser.user_type}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          <div className="p-6">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Project Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Project Name</label>
                      <p className="mt-1 text-sm text-gray-900">{project.project_name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Project Type</label>
                      <p className="mt-1 text-sm text-gray-900 capitalize">
                        {project.project_type?.replace('_', ' ')}
                      </p>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Description</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {project.description || 'No description provided'}
                      </p>
                    </div>
                  </div>
                </div>

                {project.requirements && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Requirements</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                        {typeof project.requirements === 'string' 
                          ? project.requirements 
                          : JSON.stringify(project.requirements, null, 2)
                        }
                      </pre>
                    </div>
                  </div>
                )}

                {project.notes && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Notes</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <p className="text-sm text-gray-700 whitespace-pre-wrap">{project.notes}</p>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Files Tab */}
            {activeTab === 'files' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Files & Documents</h3>
                {filesLoading ? (
                  <LoadingSpinner />
                ) : files.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {files.map((file) => (
                      <div key={file.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center space-x-3">
                          <FiFileText className="w-8 h-8 text-gray-400" />
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{file.name}</h4>
                            <p className="text-sm text-gray-500">{file.type}</p>
                          </div>
                        </div>
                        <div className="mt-3 flex justify-between items-center">
                          <span className="text-xs text-gray-500">
                            {formatDate(file.created_at)}
                          </span>
                          <a
                            href={file.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            Download
                          </a>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FiFileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">No files uploaded yet.</p>
                  </div>
                )}
              </div>
            )}

            {/* Timeline Tab */}
            {activeTab === 'timeline' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Project Timeline</h3>
                <div className="text-center py-8">
                  <FiClock className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">Project timeline will be displayed here.</p>
                  <p className="text-sm text-gray-400 mt-2">
                    This will show project milestones, status changes, and important events.
                  </p>
                </div>
              </div>
            )}

            {/* Communication Tab */}
            {activeTab === 'communication' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Communication History</h3>
                <div className="text-center py-8">
                  <FiMessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">Communication history will be displayed here.</p>
                  <p className="text-sm text-gray-400 mt-2">
                    This will show WhatsApp conversations, emails, and call logs related to this project.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ProjectDetail;
