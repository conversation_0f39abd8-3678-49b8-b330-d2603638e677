import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { Link } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import { toast } from "react-hot-toast";
import { FiPlus, FiEdit, FiTrash2, FiEye, FiSearch, FiFilter, FiDownload, FiToggleLeft, FiToggleRight, FiStar } from "react-icons/fi";

import { apiService } from "../services/api";
import { usePermissions } from "../hooks/usePermissions";
import LoadingSpinner from "../components/common/LoadingSpinner";
import DataTable from "../components/common/DataTable";
import StatusBadge from "../components/common/StatusBadge";
import Modal from "../components/common/Modal";

const Products = () => {
  const { can } = usePermissions();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState({
    search: "",
    category: "",
    status: "",
    featured: "",
    page: 1,
    limit: 20,
  });

  const [selectedProducts, setSelectedProducts] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);

  // Fetch products
  const {
    data: productsData,
    isLoading,
    error,
  } = useQuery(["products", filters], () => apiService.products.getAll(filters), {
    keepPreviousData: true,
    onError: (error) => {
      console.error("Error fetching products:", error);
      toast.error("Failed to load products");
    },
  });

  // Fetch categories for filter
  const { data: categoriesData } = useQuery("categories", () => apiService.categories.getAll({ limit: 100 }));

  // Delete product mutation
  const deleteProductMutation = useMutation((productId) => apiService.products.delete(productId), {
    onSuccess: () => {
      queryClient.invalidateQueries("products");
      toast.success("Product deleted successfully");
      setShowDeleteModal(false);
      setProductToDelete(null);
    },
    onError: (error) => {
      console.error("Error deleting product:", error);
      toast.error("Failed to delete product");
    },
  });

  // Toggle status mutation
  const toggleStatusMutation = useMutation((productId) => apiService.products.toggleStatus(productId), {
    onSuccess: () => {
      queryClient.invalidateQueries("products");
      toast.success("Product status updated");
    },
    onError: (error) => {
      console.error("Error updating product status:", error);
      toast.error("Failed to update product status");
    },
  });

  // Toggle featured mutation
  const toggleFeaturedMutation = useMutation((productId) => apiService.products.toggleFeatured(productId), {
    onSuccess: () => {
      queryClient.invalidateQueries("products");
      toast.success("Product featured status updated");
    },
    onError: (error) => {
      console.error("Error updating featured status:", error);
      toast.error("Failed to update featured status");
    },
  });

  const products = productsData?.data?.products || [];
  const pagination = productsData?.data?.pagination || {};
  const categories = categoriesData?.data?.categories || [];

  const handleSearch = (searchTerm) => {
    setFilters((prev) => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleDeleteProduct = (product) => {
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (productToDelete) {
      deleteProductMutation.mutate(productToDelete.id);
    }
  };

  const handleToggleStatus = (productId) => {
    toggleStatusMutation.mutate(productId);
  };

  const handleToggleFeatured = (productId) => {
    toggleFeaturedMutation.mutate(productId);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 0,
    }).format(price);
  };

  const columns = [
    {
      header: "Product",
      accessor: "name",
      cell: (product) => (
        <div className="flex items-center">
          <img src={product.images?.[0]?.thumbnail_url || "/images/placeholder-product.jpg"} alt={product.name} className="w-12 h-12 rounded-lg object-cover mr-3" />
          <div>
            <div className="font-medium text-gray-900">{product.name}</div>
            <div className="text-sm text-gray-500">SKU: {product.sku || "N/A"}</div>
          </div>
        </div>
      ),
    },
    {
      header: "Category",
      accessor: "category",
      cell: (product) => <span className="text-sm text-gray-600">{product.category?.name || "Uncategorized"}</span>,
    },
    {
      header: "Price",
      accessor: "price",
      cell: (product) => (
        <div>
          <div className="font-medium">{formatPrice(product.price)}</div>
          {product.compare_price && product.compare_price > product.price && <div className="text-sm text-gray-500 line-through">{formatPrice(product.compare_price)}</div>}
        </div>
      ),
    },
    {
      header: "Status",
      accessor: "is_active",
      cell: (product) => <StatusBadge status={product.is_active ? "active" : "inactive"} type="default" />,
    },
    {
      header: "Featured",
      accessor: "is_featured",
      cell: (product) => (
        <button onClick={() => handleToggleFeatured(product.id)} className={`p-1 rounded ${product.is_featured ? "text-yellow-500" : "text-gray-400"}`} disabled={!can.editProducts()}>
          <FiStar className={`w-5 h-5 ${product.is_featured ? "fill-current" : ""}`} />
        </button>
      ),
    },
    {
      header: "Views",
      accessor: "view_count",
      cell: (product) => <span className="text-sm text-gray-600">{product.view_count || 0}</span>,
    },
    {
      header: "Actions",
      accessor: "actions",
      cell: (product) => (
        <div className="flex items-center space-x-2">
          <Link to={`/products/${product.id}`} className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg" title="View Product">
            <FiEye className="w-4 h-4" />
          </Link>

          {can.editProducts() && (
            <Link to={`/products/${product.id}/edit`} className="p-2 text-green-600 hover:bg-green-50 rounded-lg" title="Edit Product">
              <FiEdit className="w-4 h-4" />
            </Link>
          )}

          {can.editProducts() && (
            <button
              onClick={() => handleToggleStatus(product.id)}
              className={`p-2 rounded-lg ${product.is_active ? "text-orange-600 hover:bg-orange-50" : "text-green-600 hover:bg-green-50"}`}
              title={product.is_active ? "Deactivate" : "Activate"}
            >
              {product.is_active ? <FiToggleRight className="w-4 h-4" /> : <FiToggleLeft className="w-4 h-4" />}
            </button>
          )}

          {can.deleteProducts() && (
            <button onClick={() => handleDeleteProduct(product)} className="p-2 text-red-600 hover:bg-red-50 rounded-lg" title="Delete Product">
              <FiTrash2 className="w-4 h-4" />
            </button>
          )}
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Products</h3>
          <p className="text-gray-600 mb-4">Something went wrong while loading products.</p>
          <button onClick={() => window.location.reload()} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Products | Interior Design Portal</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Products</h1>
            <p className="text-gray-600">Manage your product catalog</p>
          </div>

          {can.createProducts() && (
            <Link to="/products/new" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
              <FiPlus className="w-4 h-4" />
              <span>Add Product</span>
            </Link>
          )}
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search products..."
                value={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange("category", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>

            {/* Status Filter */}
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange("status", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="draft">Draft</option>
            </select>

            {/* Featured Filter */}
            <select
              value={filters.featured}
              onChange={(e) => handleFilterChange("featured", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Products</option>
              <option value="true">Featured Only</option>
              <option value="false">Non-Featured</option>
            </select>
          </div>
        </div>

        {/* Products Table */}
        <div className="bg-white rounded-lg shadow">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <LoadingSpinner size="large" />
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={products}
              pagination={pagination}
              onPageChange={handlePageChange}
              selectedItems={selectedProducts}
              onSelectionChange={setSelectedProducts}
              emptyMessage="No products found"
            />
          )}
        </div>

        {/* Delete Confirmation Modal */}
        <Modal isOpen={showDeleteModal} onClose={() => setShowDeleteModal(false)} title="Delete Product">
          <div className="space-y-4">
            <p className="text-gray-600">Are you sure you want to delete "{productToDelete?.name}"? This action cannot be undone.</p>
            <div className="flex justify-end space-x-3">
              <button onClick={() => setShowDeleteModal(false)} className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                Cancel
              </button>
              <button onClick={confirmDelete} disabled={deleteProductMutation.isLoading} className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50">
                {deleteProductMutation.isLoading ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        </Modal>
      </div>
    </>
  );
};

export default Products;
