import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { Helmet } from "react-helmet-async";
import { toast } from "react-hot-toast";
import { FiSave, FiX, FiUpload, FiTrash2 } from "react-icons/fi";

import { apiService } from "../services/api";

import LoadingSpinner from "../components/common/LoadingSpinner";

const CategoryForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const isEdit = Boolean(id);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    slug: "",
    parentId: "",
    sortOrder: 0,
    status: "active",
    metaTitle: "",
    metaDescription: "",
    isRoomType: false,
  });

  const [image, setImage] = useState(null);
  const [currentImage, setCurrentImage] = useState(null);
  const [loading, setLoading] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);

  // Fetch category data for editing
  const { data: category, isLoading: categoryLoading } = useQuery(["category", id], () => apiService.categories.getById(id), {
    enabled: isEdit,
    onSuccess: (data) => {
      const category = data.data.category;
      setFormData({
        name: category.name || "",
        description: category.description || "",
        slug: category.slug || "",
        parentId: category.parent_id || "",
        sortOrder: category.sort_order || 0,
        status: category.status || "active",
        metaTitle: category.meta_title || "",
        metaDescription: category.meta_description || "",
        isRoomType: category.is_room_type || false,
      });
      setCurrentImage(category.image_url);
    },
  });

  // Fetch parent categories
  const { data: categoriesData } = useQuery("categories", () => apiService.categories.getAll({ limit: 100 }));

  // Create/Update category mutation
  const saveCategoryMutation = useMutation(
    (categoryData) => {
      if (isEdit) {
        return apiService.categories.update(id, categoryData);
      } else {
        return apiService.categories.create(categoryData);
      }
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries("categories");
        toast.success(isEdit ? "Category updated successfully" : "Category created successfully");
        navigate("/categories");
      },
      onError: (error) => {
        console.error("Error saving category:", error);
        toast.error("Failed to save category");
      },
    }
  );

  const categories = categoriesData?.data?.categories || [];
  const availableParents = categories.filter((cat) => cat.id !== id); // Exclude self from parent options

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    // Auto-generate slug from name
    if (name === "name" && !isEdit) {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
      setFormData((prev) => ({ ...prev, slug }));
    }
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImage(file);
    }
  };

  const removeImage = () => {
    setImage(null);
    setCurrentImage(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // First create/update the category without image
      const categoryData = {
        name: formData.name,
        description: formData.description,
        slug: formData.slug,
        parent_id: formData.parentId || null,
        sort_order: parseInt(formData.sortOrder),
        is_active: formData.status === "active",
        meta_title: formData.metaTitle,
        meta_description: formData.metaDescription,
        is_room_type: formData.isRoomType,
      };

      const result = await saveCategoryMutation.mutateAsync(categoryData);
      const categoryId = isEdit ? id : result.data.category.id;

      // Upload new image if selected
      if (image) {
        setUploadingImage(true);
        try {
          await apiService.upload.categoryImage(categoryId, image);
          toast.success("Category image uploaded successfully");
        } catch (uploadError) {
          console.error("Error uploading image:", uploadError);
          toast.error("Category saved but failed to upload image");
        } finally {
          setUploadingImage(false);
        }
      }

      // Navigate to categories page
      navigate("/categories");
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setLoading(false);
    }
  };

  if (isEdit && categoryLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>{isEdit ? "Edit Category" : "Add Category"} | Interior Design Portal</title>
      </Helmet>

      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{isEdit ? "Edit Category" : "Add New Category"}</h1>
            <p className="text-gray-600">{isEdit ? "Update category information" : "Create a new category for organizing products"}</p>
          </div>
          <button onClick={() => navigate("/categories")} className="text-gray-600 hover:text-gray-800">
            <FiX className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h2>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category Name *</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
                <input
                  type="text"
                  name="slug"
                  value={formData.slug}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">URL-friendly version of the name. Only lowercase letters, numbers, and hyphens.</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Parent Category</label>
                  <select
                    name="parentId"
                    value={formData.parentId}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Root Category</option>
                    {availableParents.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                  <input
                    type="number"
                    name="sortOrder"
                    value={formData.sortOrder}
                    onChange={handleInputChange}
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>

              <div>
                <label className="flex items-center">
                  <input type="checkbox" name="isRoomType" checked={formData.isRoomType} onChange={handleInputChange} className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                  <span className="ml-2 text-sm text-gray-700">Room Type Category (Living Room, Bedroom, etc.)</span>
                </label>
              </div>
            </div>
          </div>

          {/* Category Image */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Category Image</h2>

            {/* Current Image */}
            {currentImage && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Current Image</h3>
                <div className="relative inline-block">
                  <img src={currentImage} alt="Current category" className="w-32 h-32 object-cover rounded-lg" />
                  <button type="button" onClick={removeImage} className="absolute top-2 right-2 bg-red-600 text-white p-1 rounded-full hover:bg-red-700">
                    <FiTrash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            )}

            {/* New Image */}
            {image && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-2">New Image</h3>
                <div className="relative inline-block">
                  <img src={URL.createObjectURL(image)} alt="New category" className="w-32 h-32 object-cover rounded-lg" />
                  <button type="button" onClick={() => setImage(null)} className="absolute top-2 right-2 bg-red-600 text-white p-1 rounded-full hover:bg-red-700">
                    <FiTrash2 className="w-3 h-3" />
                  </button>
                </div>
              </div>
            )}

            {/* Upload Button */}
            {!image && (
              <div>
                <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <FiUpload className="w-8 h-8 mb-2 text-gray-500" />
                    <p className="mb-2 text-sm text-gray-500">
                      <span className="font-semibold">Click to upload</span> category image
                    </p>
                    <p className="text-xs text-gray-500">PNG, JPG, WEBP up to 2MB</p>
                  </div>
                  <input type="file" accept="image/*" onChange={handleImageUpload} className="hidden" />
                </label>
              </div>
            )}
          </div>

          {/* SEO */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">SEO</h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                <input
                  type="text"
                  name="metaTitle"
                  value={formData.metaTitle}
                  onChange={handleInputChange}
                  maxLength={60}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">{formData.metaTitle.length}/60 characters</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                <textarea
                  name="metaDescription"
                  value={formData.metaDescription}
                  onChange={handleInputChange}
                  maxLength={160}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">{formData.metaDescription.length}/160 characters</p>
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-4">
            <button type="button" onClick={() => navigate("/categories")} className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || saveCategoryMutation.isLoading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
            >
              <FiSave className="w-4 h-4" />
              <span>{loading || saveCategoryMutation.isLoading ? "Saving..." : isEdit ? "Update Category" : "Create Category"}</span>
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

export default CategoryForm;
