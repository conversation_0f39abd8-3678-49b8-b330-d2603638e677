import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { Link } from "react-router-dom";
import { Helmet } from "react-helmet-async";
import { toast } from "react-hot-toast";
import { FiPlus, FiEdit, FiTrash2, FiEye, FiSearch, FiToggleLeft, FiToggleRight, FiFolder, FiFolderPlus } from "react-icons/fi";

import { apiService } from "../services/api";
import { usePermissions } from "../hooks/usePermissions";
import LoadingSpinner from "../components/common/LoadingSpinner";
import DataTable from "../components/common/DataTable";
import StatusBadge from "../components/common/StatusBadge";
import Modal from "../components/common/Modal";

const Categories = () => {
  const { can } = usePermissions();
  const queryClient = useQueryClient();

  const [filters, setFilters] = useState({
    search: "",
    status: "",
    page: 1,
    limit: 20,
  });

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState(null);

  // Fetch categories
  const {
    data: categoriesData,
    isLoading,
    error,
  } = useQuery(["categories", filters], () => apiService.categories.getAll(filters), {
    keepPreviousData: true,
    onError: (error) => {
      console.error("Error fetching categories:", error);
      toast.error("Failed to load categories");
    },
  });

  // Delete category mutation
  const deleteCategoryMutation = useMutation((categoryId) => apiService.categories.delete(categoryId), {
    onSuccess: () => {
      queryClient.invalidateQueries("categories");
      toast.success("Category deleted successfully");
      setShowDeleteModal(false);
      setCategoryToDelete(null);
    },
    onError: (error) => {
      console.error("Error deleting category:", error);
      toast.error("Failed to delete category");
    },
  });

  // Toggle status mutation
  const toggleStatusMutation = useMutation((categoryId) => apiService.categories.toggleStatus(categoryId), {
    onSuccess: () => {
      queryClient.invalidateQueries("categories");
      toast.success("Category status updated");
    },
    onError: (error) => {
      console.error("Error updating category status:", error);
      toast.error("Failed to update category status");
    },
  });

  const categories = categoriesData?.data?.categories || [];
  const pagination = categoriesData?.data?.pagination || {};

  const handleSearch = (searchTerm) => {
    setFilters((prev) => ({ ...prev, search: searchTerm, page: 1 }));
  };

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  const handlePageChange = (page) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleDeleteCategory = (category) => {
    setCategoryToDelete(category);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (categoryToDelete) {
      deleteCategoryMutation.mutate(categoryToDelete.id);
    }
  };

  const handleToggleStatus = (categoryId) => {
    toggleStatusMutation.mutate(categoryId);
  };

  const columns = [
    {
      header: "Category",
      accessor: "name",
      cell: (category) => (
        <div className="flex items-center">
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
            {category.image_url ? <img src={category.image_url} alt={category.name} className="w-12 h-12 rounded-lg object-cover" /> : <FiFolder className="w-6 h-6 text-gray-400" />}
          </div>
          <div>
            <div className="font-medium text-gray-900">{category.name}</div>
            <div className="text-sm text-gray-500">{category.slug}</div>
          </div>
        </div>
      ),
    },
    {
      header: "Description",
      accessor: "description",
      cell: (category) => (
        <div className="max-w-xs">
          <p className="text-sm text-gray-600 truncate">{category.description || "No description"}</p>
        </div>
      ),
    },
    {
      header: "Parent Category",
      accessor: "parent",
      cell: (category) => <span className="text-sm text-gray-600">{category.parent?.name || "Root Category"}</span>,
    },
    {
      header: "Products",
      accessor: "product_count",
      cell: (category) => <span className="text-sm text-gray-600">{category.product_count || 0} products</span>,
    },
    {
      header: "Status",
      accessor: "is_active",
      cell: (category) => <StatusBadge status={category.is_active ? "active" : "inactive"} type="default" />,
    },
    {
      header: "Sort Order",
      accessor: "sort_order",
      cell: (category) => <span className="text-sm text-gray-600">{category.sort_order || 0}</span>,
    },
    {
      header: "Actions",
      accessor: "actions",
      cell: (category) => (
        <div className="flex items-center space-x-2">
          <Link to={`/categories/${category.id}`} className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg" title="View Category">
            <FiEye className="w-4 h-4" />
          </Link>

          {can.editCategories() && (
            <Link to={`/categories/${category.id}/edit`} className="p-2 text-green-600 hover:bg-green-50 rounded-lg" title="Edit Category">
              <FiEdit className="w-4 h-4" />
            </Link>
          )}

          {can.editCategories() && (
            <button
              onClick={() => handleToggleStatus(category.id)}
              className={`p-2 rounded-lg ${category.status === "active" ? "text-orange-600 hover:bg-orange-50" : "text-green-600 hover:bg-green-50"}`}
              title={category.status === "active" ? "Deactivate" : "Activate"}
            >
              {category.status === "active" ? <FiToggleRight className="w-4 h-4" /> : <FiToggleLeft className="w-4 h-4" />}
            </button>
          )}

          {can.deleteCategories() && (
            <button onClick={() => handleDeleteCategory(category)} className="p-2 text-red-600 hover:bg-red-50 rounded-lg" title="Delete Category">
              <FiTrash2 className="w-4 h-4" />
            </button>
          )}
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Categories</h3>
          <p className="text-gray-600 mb-4">Something went wrong while loading categories.</p>
          <button onClick={() => window.location.reload()} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Categories | Interior Design Portal</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Categories</h1>
            <p className="text-gray-600">Organize your products into categories</p>
          </div>

          {can.createCategories() && (
            <Link to="/categories/new" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
              <FiPlus className="w-4 h-4" />
              <span>Add Category</span>
            </Link>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FiFolder className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Categories</p>
                <p className="text-2xl font-bold text-gray-900">{pagination.total || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <FiFolderPlus className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Categories</p>
                <p className="text-2xl font-bold text-gray-900">{categories.filter((c) => c.status === "active").length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <FiFolder className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Root Categories</p>
                <p className="text-2xl font-bold text-gray-900">{categories.filter((c) => !c.parent_id).length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <FiFolder className="w-6 h-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Subcategories</p>
                <p className="text-2xl font-bold text-gray-900">{categories.filter((c) => c.parent_id).length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search categories..."
                value={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Status Filter */}
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange("status", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>

            {/* Clear Filters */}
            <button onClick={() => setFilters({ search: "", status: "", page: 1, limit: 20 })} className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
              Clear Filters
            </button>
          </div>
        </div>

        {/* Categories Table */}
        <div className="bg-white rounded-lg shadow">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <LoadingSpinner size="large" />
            </div>
          ) : (
            <DataTable columns={columns} data={categories} pagination={pagination} onPageChange={handlePageChange} emptyMessage="No categories found" />
          )}
        </div>

        {/* Delete Confirmation Modal */}
        <Modal isOpen={showDeleteModal} onClose={() => setShowDeleteModal(false)} title="Delete Category">
          <div className="space-y-4">
            <p className="text-gray-600">Are you sure you want to delete "{categoryToDelete?.name}"? This action cannot be undone.</p>
            {categoryToDelete?.product_count > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-yellow-800 text-sm">
                  <strong>Warning:</strong> This category contains {categoryToDelete.product_count} products. Deleting it will remove the category association from these products.
                </p>
              </div>
            )}
            <div className="flex justify-end space-x-3">
              <button onClick={() => setShowDeleteModal(false)} className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                Cancel
              </button>
              <button onClick={confirmDelete} disabled={deleteCategoryMutation.isLoading} className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50">
                {deleteCategoryMutation.isLoading ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        </Modal>
      </div>
    </>
  );
};

export default Categories;
