import React from 'react';
import { Helmet } from 'react-helmet-async';
import { 
  <PERSON>Bar<PERSON>hart, 
  FiTrendingUp, 
  FiPieChart,
  FiActivity,
  FiUsers,
  FiShoppingBag
} from 'react-icons/fi';

const Analytics = () => {
  return (
    <>
      <Helmet>
        <title>Analytics | Interior Design Portal</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600">Comprehensive business insights and reporting</p>
        </div>

        {/* Coming Soon Card */}
        <div className="bg-white rounded-lg shadow p-12">
          <div className="text-center">
            <div className="mx-auto h-24 w-24 text-blue-500 mb-6">
              <FiBarChart className="w-full h-full" />
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Advanced Analytics Coming Soon
            </h2>
            
            <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
              We're building comprehensive analytics and reporting features to help you understand 
              your business performance, customer behavior, and growth opportunities.
            </p>

            {/* Feature Preview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FiTrendingUp className="w-6 h-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Sales Analytics</h3>
                <p className="text-sm text-gray-600">
                  Track revenue, conversion rates, and sales performance over time
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FiUsers className="w-6 h-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Customer Insights</h3>
                <p className="text-sm text-gray-600">
                  Understand customer behavior, preferences, and lifetime value
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FiShoppingBag className="w-6 h-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Product Performance</h3>
                <p className="text-sm text-gray-600">
                  Analyze product views, popularity, and inventory trends
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FiPieChart className="w-6 h-6 text-orange-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Project Analytics</h3>
                <p className="text-sm text-gray-600">
                  Monitor project completion rates, timelines, and profitability
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FiActivity className="w-6 h-6 text-red-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Real-time Metrics</h3>
                <p className="text-sm text-gray-600">
                  Live dashboard with real-time business metrics and KPIs
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FiBarChart className="w-6 h-6 text-indigo-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Custom Reports</h3>
                <p className="text-sm text-gray-600">
                  Generate custom reports and export data for deeper analysis
                </p>
              </div>
            </div>

            {/* Timeline */}
            <div className="mt-12 p-6 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Development Timeline</h3>
              <p className="text-blue-700 text-sm">
                Analytics features are currently in development and will be available in the next major update. 
                Stay tuned for powerful insights to grow your interior design business!
              </p>
            </div>
          </div>
        </div>

        {/* Quick Stats from Dashboard */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Overview</h3>
          <p className="text-gray-600 mb-4">
            For now, you can view basic statistics on the Dashboard. Advanced analytics with 
            detailed charts, trends, and insights will be available soon.
          </p>
          <a
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <FiBarChart className="w-4 h-4 mr-2" />
            View Dashboard Stats
          </a>
        </div>
      </div>
    </>
  );
};

export default Analytics;
