import { useAuth } from '../context/AuthContext';

// Permission mapping based on user types
const PERMISSIONS = {
  admin: {
    // Full access to everything
    viewDashboard: true,
    viewProducts: true,
    createProducts: true,
    editProducts: true,
    deleteProducts: true,
    viewCategories: true,
    createCategories: true,
    editCategories: true,
    deleteCategories: true,
    viewCustomers: true,
    editCustomers: true,
    deleteCustomers: true,
    viewProjects: true,
    createProjects: true,
    editProjects: true,
    deleteProjects: true,
    viewWhatsApp: true,
    manageWhatsApp: true,
    viewAnalytics: true,
    viewSettings: true,
    manageSettings: true,
    manageUsers: true,
    viewReports: true,
    exportData: true
  },
  designer: {
    // Design-focused permissions
    viewDashboard: true,
    viewProducts: true,
    createProducts: false,
    editProducts: false,
    deleteProducts: false,
    viewCategories: true,
    createCategories: false,
    editCategories: false,
    deleteCategories: false,
    viewCustomers: true,
    editCustomers: false,
    deleteCustomers: false,
    viewProjects: true,
    createProjects: true,
    editProjects: true,
    deleteProjects: false,
    viewWhatsApp: true,
    manageWhatsApp: true,
    viewAnalytics: true,
    viewSettings: false,
    manageSettings: false,
    manageUsers: false,
    viewReports: true,
    exportData: false
  },
  sales: {
    // Sales-focused permissions
    viewDashboard: true,
    viewProducts: true,
    createProducts: false,
    editProducts: false,
    deleteProducts: false,
    viewCategories: true,
    createCategories: false,
    editCategories: false,
    deleteCategories: false,
    viewCustomers: true,
    editCustomers: true,
    deleteCustomers: false,
    viewProjects: true,
    createProjects: true,
    editProjects: true,
    deleteProjects: false,
    viewWhatsApp: true,
    manageWhatsApp: true,
    viewAnalytics: true,
    viewSettings: false,
    manageSettings: false,
    manageUsers: false,
    viewReports: true,
    exportData: false
  },
  customer: {
    // Very limited permissions (shouldn't access portal)
    viewDashboard: false,
    viewProducts: false,
    createProducts: false,
    editProducts: false,
    deleteProducts: false,
    viewCategories: false,
    createCategories: false,
    editCategories: false,
    deleteCategories: false,
    viewCustomers: false,
    editCustomers: false,
    deleteCustomers: false,
    viewProjects: false,
    createProjects: false,
    editProjects: false,
    deleteProjects: false,
    viewWhatsApp: false,
    manageWhatsApp: false,
    viewAnalytics: false,
    viewSettings: false,
    manageSettings: false,
    manageUsers: false,
    viewReports: false,
    exportData: false
  }
};

export const usePermissions = () => {
  const { user } = useAuth();

  const userType = user?.user_type || 'customer';
  const userPermissions = PERMISSIONS[userType] || PERMISSIONS.customer;

  // Helper function to check if user has a specific permission
  const hasPermission = (permission) => {
    return userPermissions[permission] || false;
  };

  // Helper function to check multiple permissions (all must be true)
  const hasAllPermissions = (permissions) => {
    return permissions.every(permission => hasPermission(permission));
  };

  // Helper function to check multiple permissions (at least one must be true)
  const hasAnyPermission = (permissions) => {
    return permissions.some(permission => hasPermission(permission));
  };

  // Specific permission checkers for common use cases
  const can = {
    // Dashboard
    viewDashboard: () => hasPermission('viewDashboard'),

    // Products
    viewProducts: () => hasPermission('viewProducts'),
    createProducts: () => hasPermission('createProducts'),
    editProducts: () => hasPermission('editProducts'),
    deleteProducts: () => hasPermission('deleteProducts'),
    manageProducts: () => hasAnyPermission(['createProducts', 'editProducts', 'deleteProducts']),

    // Categories
    viewCategories: () => hasPermission('viewCategories'),
    createCategories: () => hasPermission('createCategories'),
    editCategories: () => hasPermission('editCategories'),
    deleteCategories: () => hasPermission('deleteCategories'),
    manageCategories: () => hasAnyPermission(['createCategories', 'editCategories', 'deleteCategories']),

    // Customers
    viewCustomers: () => hasPermission('viewCustomers'),
    editCustomers: () => hasPermission('editCustomers'),
    deleteCustomers: () => hasPermission('deleteCustomers'),
    manageCustomers: () => hasAnyPermission(['editCustomers', 'deleteCustomers']),

    // Projects
    viewProjects: () => hasPermission('viewProjects'),
    createProjects: () => hasPermission('createProjects'),
    editProjects: () => hasPermission('editProjects'),
    deleteProjects: () => hasPermission('deleteProjects'),
    manageProjects: () => hasAnyPermission(['createProjects', 'editProjects', 'deleteProjects']),

    // WhatsApp
    viewWhatsApp: () => hasPermission('viewWhatsApp'),
    manageWhatsApp: () => hasPermission('manageWhatsApp'),

    // Analytics
    viewAnalytics: () => hasPermission('viewAnalytics'),
    viewReports: () => hasPermission('viewReports'),
    exportData: () => hasPermission('exportData'),

    // Settings
    viewSettings: () => hasPermission('viewSettings'),
    manageSettings: () => hasPermission('manageSettings'),
    manageUsers: () => hasPermission('manageUsers'),

    // Admin functions
    isAdmin: () => userType === 'admin',
    isDesigner: () => userType === 'designer',
    isSales: () => userType === 'sales',
    isStaff: () => ['admin', 'designer', 'sales'].includes(userType)
  };

  return {
    can,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    userPermissions,
    userType
  };
};

export default usePermissions;