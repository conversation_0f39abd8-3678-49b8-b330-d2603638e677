// src/context/AuthContext.jsx (Portal App)
import React, { createContext, useContext, useEffect, useState } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import { apiService } from "../services/api";
import { toast } from "react-hot-toast";

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const { user: auth0User, isAuthenticated, isLoading: auth0Loading, loginWithRedirect, logout: auth0Logout, getAccessTokenSilently } = useAuth0();

  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [permissions, setPermissions] = useState([]);

  // Sync user data with backend
  const syncUser = async () => {
    if (!isAuthenticated) {
      setUser(null);
      setPermissions([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      const token = await getAccessTokenSilently();
      localStorage.setItem("auth0_token", token);

      const response = await apiService.auth.getProfile();
      const userData = response.data.user;
      const userPermissions = response.data.permissions;

      // Check if user can access portal
      if (!userData.canAccessPortal) {
        console.warn("User doesn't have portal access, but allowing login for development");
        toast.error("Limited portal access - contact admin for full permissions");
        // Don't logout immediately, let them see the portal but with limited access
        // await logout();
        // return;
      }

      setUser(userData);
      setPermissions(userPermissions);
      localStorage.setItem("user_data", JSON.stringify(userData));
    } catch (error) {
      console.error("Failed to sync user:", error);
      // Only logout on actual authentication errors, not all 401s
      if ((error.response?.status === 401 && error.response?.data?.isAuthError) || error.response?.status === 403) {
        await logout();
      }
    } finally {
      setLoading(false);
    }
  };

  const login = async () => {
    await loginWithRedirect({
      appState: {
        returnTo: window.location.pathname,
      },
    });
  };

  const logout = async () => {
    localStorage.removeItem("auth0_token");
    localStorage.removeItem("user_data");
    setUser(null);
    setPermissions([]);

    await auth0Logout({
      logoutParams: {
        returnTo: window.location.origin,
      },
    });
  };

  const updateProfile = async (profileData) => {
    try {
      const response = await apiService.auth.updateProfile(profileData);
      const updatedUser = response.data.user;

      setUser(updatedUser);
      localStorage.setItem("user_data", JSON.stringify(updatedUser));

      toast.success("Profile updated successfully");
      return updatedUser;
    } catch (error) {
      console.error("Failed to update profile:", error);
      throw error;
    }
  };

  const hasPermission = (permission) => {
    return permissions.includes(permission) || user?.user_type === "admin";
  };

  const isAdmin = () => user?.user_type === "admin";
  const isDesigner = () => user?.user_type === "designer";
  const isSales = () => user?.user_type === "sales";

  const getFullName = () => {
    if (!user) return "";
    return `${user.first_name} ${user.last_name || ""}`.trim();
  };

  useEffect(() => {
    if (!auth0Loading) {
      syncUser();
    }
  }, [isAuthenticated, auth0Loading]);

  const value = {
    user,
    permissions,
    auth0User,
    loading: auth0Loading || loading,
    isAuthenticated,
    login,
    logout,
    syncUser,
    updateProfile,
    hasPermission,
    isAdmin,
    isDesigner,
    isSales,
    getFullName,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
