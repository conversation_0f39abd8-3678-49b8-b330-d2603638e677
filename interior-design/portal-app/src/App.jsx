import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Auth0Provider } from '@auth0/auth0-react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';
import { HelmetProvider } from 'react-helmet-async';

import { auth0Config } from './config/auth0';
import { AuthProvider } from './context/AuthContext';
import { DataProvider } from './context/DataContext';

// src/App.jsx (Portal App)


// Layout Components
import Sidebar from './components/layout/Sidebar';
import Header from './components/layout/Header';
import LoadingSpinner from './components/common/LoadingSpinner';

// Pages
import Dashboard from './pages/Dashboard';
import Products from './pages/Products';
import ProductForm from './pages/ProductForm';
import Categories from './pages/Categories';
import CategoryForm from './pages/CategoryForm';
import Customers from './pages/Customers';
import CustomerDetail from './pages/CustomerDetail';
import Projects from './pages/Projects';
import ProjectDetail from './pages/ProjectDetail';
import ProjectForm from './pages/ProjectForm';
import WhatsApp from './pages/WhatsApp';
import WhatsAppConversation from './pages/WhatsAppConversation';
import Analytics from './pages/Analytics';
import Settings from './pages/Settings';
import Profile from './pages/Profile';
import NotFound from './pages/NotFound';

// Auth components
import AuthCallback from './components/auth/AuthCallback';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});
// Auth0 configuration for portal
const portalAuth0Config = {
  ...auth0Config,
  clientId: process.env.REACT_APP_AUTH0_PORTAL_CLIENT_ID,
  scope: 'openid profile email write:products write:categories read:customers manage:users read:analytics write:whatsapp'
};
function App() {
  return (
    <HelmetProvider>
      <Auth0Provider
        domain={portalAuth0Config.domain}
        clientId={portalAuth0Config.clientId}
        audience={portalAuth0Config.audience}
        redirectUri={portalAuth0Config.redirectUri}
        scope={portalAuth0Config.scope}
        useRefreshTokens={portalAuth0Config.useRefreshTokens}
        cacheLocation={portalAuth0Config.cacheLocation}
      >
        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <DataProvider>
              <Router>
                <Routes>
                  {/* Auth callback route */}
                  <Route path="/callback" element={<AuthCallback />} />
                  
                  {/* Protected portal routes */}
                  <Route path="/*" element={
                    <ProtectedRoute>
                      <PortalLayout />
                    </ProtectedRoute>
                  } />
                </Routes>
                
                {/* Toast Notifications */}
                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: '#363636',
                      color: '#fff',
                    },
                    success: {
                      duration: 3000,
                      theme: {
                        primary: '#10B981',
                        secondary: '#FFFFFF',
                      },
                    },
                    error: {
                      duration: 5000,
                      theme: {
                        primary: '#EF4444',
                        secondary: '#FFFFFF',
                      },
                    },
                  }}
                />
              </Router>
            </DataProvider>
          </AuthProvider>
        </QueryClientProvider>
      </Auth0Provider>
    </HelmetProvider>
  );
}

// Portal Layout Component
const PortalLayout = () => {
  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header />
        
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
          <Routes>
            {/* Dashboard */}
            <Route path="/" element={<Dashboard />} />
            
            {/* Products */}
            <Route path="/products" element={<Products />} />
            <Route path="/products/new" element={<ProductForm />} />
            <Route path="/products/:id/edit" element={<ProductForm />} />
            
            {/* Categories */}
            <Route path="/categories" element={<Categories />} />
            <Route path="/categories/new" element={<CategoryForm />} />
            <Route path="/categories/:id/edit" element={<CategoryForm />} />
            
            {/* Customers */}
            <Route path="/customers" element={<Customers />} />
            <Route path="/customers/:id" element={<CustomerDetail />} />
            
            {/* Projects */}
            <Route path="/projects" element={<Projects />} />
            <Route path="/projects/new" element={<ProjectForm />} />
            <Route path="/projects/:id" element={<ProjectDetail />} />
            <Route path="/projects/:id/edit" element={<ProjectForm />} />
            
            {/* WhatsApp */}
            <Route path="/whatsapp" element={<WhatsApp />} />
            <Route path="/whatsapp/:conversationId" element={<WhatsAppConversation />} />
            
            {/* Analytics */}
            <Route path="/analytics" element={<Analytics />} />
            
            {/* Settings & Profile */}
            <Route path="/settings" element={<Settings />} />
            <Route path="/profile" element={<Profile />} />
            
            {/* 404 */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </main>
      </div>
    </div>
  );
};

export default App;