// src/services/uploadService.js
import { apiService } from "./api";

class UploadService {
  // Upload product images
  async uploadProductImages(productId, files) {
    try {
      const fileArray = Array.isArray(files) ? files : [files];
      const response = await apiService.upload.productImages(productId, fileArray);
      return response.data;
    } catch (error) {
      console.error("Product images upload error:", error);
      throw error;
    }
  }

  // Upload category image
  async uploadCategoryImage(categoryId, file) {
    try {
      const response = await apiService.upload.categoryImage(categoryId, file);
      return response.data;
    } catch (error) {
      console.error("Category image upload error:", error);
      throw error;
    }
  }

  // Upload user avatar
  async uploadUserAvatar(file) {
    try {
      const formData = new FormData();
      formData.append("avatar", file);

      const response = await api.post("/upload/user-avatar", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          console.log(`Upload Progress: ${percentCompleted}%`);
        },
      });

      return response.data;
    } catch (error) {
      console.error("User avatar upload error:", error);
      throw error;
    }
  }

  // Delete product image
  async deleteProductImage(imageId) {
    try {
      const response = await api.delete(`/upload/product-image/${imageId}`);
      return response.data;
    } catch (error) {
      console.error("Delete product image error:", error);
      throw error;
    }
  }

  // Set product image as primary
  async setPrimaryProductImage(imageId) {
    try {
      const response = await api.patch(`/upload/product-image/${imageId}/primary`);
      return response.data;
    } catch (error) {
      console.error("Set primary image error:", error);
      throw error;
    }
  }

  // Reorder product images
  async reorderProductImages(productId, imageIds) {
    try {
      const response = await api.put(`/upload/product-images/${productId}/reorder`, {
        imageIds,
      });
      return response.data;
    } catch (error) {
      console.error("Reorder images error:", error);
      throw error;
    }
  }

  // Check S3 health
  async checkS3Health() {
    try {
      const response = await api.get("/upload/health");
      return response.data;
    } catch (error) {
      console.error("S3 health check error:", error);
      throw error;
    }
  }

  // Validate file before upload
  validateFile(file, type = "image") {
    const maxSize = type === "avatar" ? 2 * 1024 * 1024 : 10 * 1024 * 1024; // 2MB for avatars, 10MB for others
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp", "image/gif"];

    if (!file) {
      throw new Error("No file provided");
    }

    if (file.size > maxSize) {
      const maxSizeMB = maxSize / (1024 * 1024);
      throw new Error(`File size too large. Maximum size is ${maxSizeMB}MB`);
    }

    if (!allowedTypes.includes(file.type)) {
      throw new Error("Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed");
    }

    return true;
  }

  // Validate multiple files
  validateFiles(files, type = "image") {
    if (!files || files.length === 0) {
      throw new Error("No files provided");
    }

    if (files.length > 10) {
      throw new Error("Too many files. Maximum 10 images per upload");
    }

    for (let i = 0; i < files.length; i++) {
      this.validateFile(files[i], type);
    }

    return true;
  }

  // Get file preview URL
  getFilePreview(file) {
    if (!file) return null;

    try {
      return URL.createObjectURL(file);
    } catch (error) {
      console.error("Error creating file preview:", error);
      return null;
    }
  }

  // Clean up file preview URL
  cleanupFilePreview(url) {
    if (url && url.startsWith("blob:")) {
      URL.revokeObjectURL(url);
    }
  }

  // Format file size for display
  formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  // Get file extension
  getFileExtension(filename) {
    return filename.slice(((filename.lastIndexOf(".") - 1) >>> 0) + 2);
  }

  // Generate thumbnail from file
  async generateThumbnail(file, maxWidth = 150, maxHeight = 150) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw image on canvas
        ctx.drawImage(img, 0, 0, width, height);

        // Convert to blob
        canvas.toBlob(resolve, "image/jpeg", 0.8);
      };

      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }
}

// Create singleton instance
const uploadService = new UploadService();

export default uploadService;
