// src/services/api.js (Portal App)
import axios from 'axios';
import { toast } from 'react-hot-toast';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth0_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;
    
    if (response?.status === 401) {
      localStorage.removeItem('auth0_token');
      window.location.href = '/login';
      return Promise.reject(error);
    }
    
    if (response?.status === 403) {
      toast.error('You don\'t have permission to perform this action');
      return Promise.reject(error);
    }
    
    if (response?.status >= 500) {
      toast.error('Server error. Please try again later.');
      return Promise.reject(error);
    }
    
    const errorMessage = response?.data?.error || 'An error occurred';
    toast.error(errorMessage);
    
    return Promise.reject(error);
  }
);

// API methods for Portal
export const apiService = {
  // Auth methods
  auth: {
    getProfile: () => api.get('/auth/me'),
    updateProfile: (data) => api.put('/auth/profile', data),
    changePassword: (data) => api.put('/auth/change-password', data),
    uploadAvatar: (formData) => api.post('/auth/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
    getUsers: (params = {}) => api.get('/auth/users', { params }),
    createUser: (data) => api.post('/auth/users', data),
    updateUser: (userId, data) => api.put(`/auth/users/${userId}`, data),
    deleteUser: (userId) => api.delete(`/auth/users/${userId}`),
    updateUserStatus: (userId, isActive) =>
      api.put(`/auth/users/${userId}/status`, { is_active: isActive }),
    resetPassword: (userId) => api.post(`/auth/users/${userId}/reset-password`),
  },

  // Product management
  products: {
    getAll: (params = {}) => api.get('/products', { params }),
    getById: (id) => api.get(`/products/${id}`),
    create: (data) => api.post('/products', data),
    update: (id, data) => api.put(`/products/${id}`, data),
    delete: (id) => api.delete(`/products/${id}`),
    toggleStatus: (id) => api.patch(`/products/${id}/toggle-status`),
    toggleFeatured: (id) => api.patch(`/products/${id}/toggle-featured`),
    getFilters: () => api.get('/products/filters'),
  },

  // Category management
  categories: {
    getAll: (params = {}) => api.get('/categories', { params }),
    getById: (id) => api.get(`/categories/${id}`),
    create: (data) => api.post('/categories', data),
    update: (id, data) => api.put(`/categories/${id}`, data),
    delete: (id) => api.delete(`/categories/${id}`),
    toggleStatus: (id) => api.patch(`/categories/${id}/toggle-status`),
    getTree: () => api.get('/categories', { params: { tree: true } }),
  },

  // Customer management
  customers: {
    getAll: (params = {}) => api.get('/customers', { params }),
    getById: (id) => api.get(`/customers/${id}`),
    update: (id, data) => api.put(`/customers/${id}`, data),
    getProjects: (customerId) => api.get(`/customers/${customerId}/projects`),
  },

  // Project management
  projects: {
    getAll: (params = {}) => api.get('/projects', { params }),
    getById: (id) => api.get(`/projects/${id}`),
    create: (data) => api.post('/projects', data),
    update: (id, data) => api.put(`/projects/${id}`, data),
    updateStatus: (id, status) => api.patch(`/projects/${id}/status`, { status }),
    assign: (id, designerId) => api.patch(`/projects/${id}/assign`, { designer_id: designerId }),
    getFiles: (projectId) => api.get(`/projects/${projectId}/files`),
  },

  // WhatsApp management
  whatsapp: {
    getConversations: (params = {}) => api.get('/whatsapp/conversations', { params }),
    getConversation: (id) => api.get(`/whatsapp/conversations/${id}`),
    getMessages: (conversationId, params = {}) => 
      api.get(`/whatsapp/conversations/${conversationId}/messages`, { params }),
    sendMessage: (conversationId, data) => 
      api.post(`/whatsapp/conversations/${conversationId}/messages`, data),
    assignConversation: (conversationId, assignedTo) => 
      api.put(`/whatsapp/conversations/${conversationId}/assign`, { assignedTo }),
    updateStatus: (conversationId, status) => 
      api.put(`/whatsapp/conversations/${conversationId}/status`, { status }),
    markAsRead: (conversationId) => 
      api.post(`/whatsapp/conversations/${conversationId}/mark-read`),
    sendTemplate: (data) => api.post('/whatsapp/send-template', data),
  },

  // Upload methods
  upload: {
    productImages: (productId, files) => {
      const formData = new FormData();
      files.forEach(file => formData.append('images', file));
      return api.post(`/upload/product-images/${productId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },
    categoryImage: (categoryId, file) => {
      const formData = new FormData();
      formData.append('image', file);
      return api.post(`/upload/category-image/${categoryId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },
    deleteProductImage: (imageId) => api.delete(`/upload/product-image/${imageId}`),
    setPrimaryImage: (imageId) => api.patch(`/upload/product-image/${imageId}/primary`),
    reorderImages: (productId, imageIds) => 
      api.put(`/upload/product-images/${productId}/reorder`, { imageIds }),
  },

  // Analytics
  analytics: {
    getDashboard: () => api.get('/analytics/dashboard'),
    getProducts: (params = {}) => api.get('/analytics/products', { params }),
    getCustomers: (params = {}) => api.get('/analytics/customers', { params }),
    getProjects: (params = {}) => api.get('/analytics/projects', { params }),
    getWhatsApp: (params = {}) => api.get('/analytics/whatsapp', { params }),
    export: (params = {}) => api.get('/analytics/export', { params }),
    getRealTime: () => api.get('/analytics/realtime'),
    getGoals: (params = {}) => api.get('/analytics/goals', { params }),
    getCohorts: (params = {}) => api.get('/analytics/cohorts', { params }),
    getRetention: (params = {}) => api.get('/analytics/retention', { params }),
    getABTests: () => api.get('/analytics/ab-tests'),
    getCustomReport: (config) => api.post('/analytics/custom-report', config),
    saveCustomReport: (config) => api.post('/analytics/saved-reports', config),
    getSavedReports: () => api.get('/analytics/saved-reports'),
  },

  // Settings management
  settings: {
    getAll: () => api.get('/settings'),
    update: (data) => api.put('/settings', data),
    get: (key) => api.get(`/settings/${key}`),
    set: (key, value) => api.put(`/settings/${key}`, { value }),
    reset: () => api.post('/settings/reset'),
    backup: () => api.get('/settings/backup'),
    restore: (data) => api.post('/settings/restore', data),
  },

  // File management
  files: {
    upload: (file, type = 'general') => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      return api.post('/files/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },
    delete: (fileId) => api.delete(`/files/${fileId}`),
    getUrl: (fileId) => api.get(`/files/${fileId}/url`),
    getMetadata: (fileId) => api.get(`/files/${fileId}/metadata`),
  },

  // Notifications
  notifications: {
    getAll: (params = {}) => api.get('/notifications', { params }),
    markAsRead: (notificationId) => api.patch(`/notifications/${notificationId}/read`),
    markAllAsRead: () => api.patch('/notifications/mark-all-read'),
    delete: (notificationId) => api.delete(`/notifications/${notificationId}`),
    getUnreadCount: () => api.get('/notifications/unread-count'),
  },

  // Reports
  reports: {
    generate: (type, params = {}) => api.post(`/reports/${type}`, params),
    getAll: (params = {}) => api.get('/reports', { params }),
    getById: (reportId) => api.get(`/reports/${reportId}`),
    download: (reportId) => api.get(`/reports/${reportId}/download`, {
      responseType: 'blob'
    }),
    delete: (reportId) => api.delete(`/reports/${reportId}`),
    schedule: (type, schedule, params = {}) =>
      api.post(`/reports/${type}/schedule`, { schedule, params }),
  },
};

export default api;