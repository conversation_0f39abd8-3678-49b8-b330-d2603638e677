// src/services/api.js (Portal App)
import axios from 'axios';
import { toast } from 'react-hot-toast';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth0_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;
    
    if (response?.status === 401) {
      localStorage.removeItem('auth0_token');
      window.location.href = '/login';
      return Promise.reject(error);
    }
    
    if (response?.status === 403) {
      toast.error('You don\'t have permission to perform this action');
      return Promise.reject(error);
    }
    
    if (response?.status >= 500) {
      toast.error('Server error. Please try again later.');
      return Promise.reject(error);
    }
    
    const errorMessage = response?.data?.error || 'An error occurred';
    toast.error(errorMessage);
    
    return Promise.reject(error);
  }
);

// API methods for Portal
export const apiService = {
  // Auth methods
  auth: {
    getProfile: () => api.get('/auth/me'),
    updateProfile: (data) => api.put('/auth/profile', data),
    getUsers: (params = {}) => api.get('/auth/users', { params }),
    updateUserStatus: (userId, isActive) => 
      api.put(`/auth/users/${userId}/status`, { is_active: isActive }),
  },

  // Product management
  products: {
    getAll: (params = {}) => api.get('/products', { params }),
    getById: (id) => api.get(`/products/${id}`),
    create: (data) => api.post('/products', data),
    update: (id, data) => api.put(`/products/${id}`, data),
    delete: (id) => api.delete(`/products/${id}`),
    toggleStatus: (id) => api.patch(`/products/${id}/toggle-status`),
    toggleFeatured: (id) => api.patch(`/products/${id}/toggle-featured`),
    getFilters: () => api.get('/products/filters'),
  },

  // Category management
  categories: {
    getAll: (params = {}) => api.get('/categories', { params }),
    getById: (id) => api.get(`/categories/${id}`),
    create: (data) => api.post('/categories', data),
    update: (id, data) => api.put(`/categories/${id}`, data),
    delete: (id) => api.delete(`/categories/${id}`),
    toggleStatus: (id) => api.patch(`/categories/${id}/toggle-status`),
    getTree: () => api.get('/categories', { params: { tree: true } }),
  },

  // Customer management
  customers: {
    getAll: (params = {}) => api.get('/customers', { params }),
    getById: (id) => api.get(`/customers/${id}`),
    update: (id, data) => api.put(`/customers/${id}`, data),
    getProjects: (customerId) => api.get(`/customers/${customerId}/projects`),
  },

  // Project management
  projects: {
    getAll: (params = {}) => api.get('/projects', { params }),
    getById: (id) => api.get(`/projects/${id}`),
    create: (data) => api.post('/projects', data),
    update: (id, data) => api.put(`/projects/${id}`, data),
    updateStatus: (id, status) => api.patch(`/projects/${id}/status`, { status }),
    assign: (id, designerId) => api.patch(`/projects/${id}/assign`, { designer_id: designerId }),
    getFiles: (projectId) => api.get(`/projects/${projectId}/files`),
  },

  // WhatsApp management
  whatsapp: {
    getConversations: (params = {}) => api.get('/whatsapp/conversations', { params }),
    getConversation: (id) => api.get(`/whatsapp/conversations/${id}`),
    getMessages: (conversationId, params = {}) => 
      api.get(`/whatsapp/conversations/${conversationId}/messages`, { params }),
    sendMessage: (conversationId, data) => 
      api.post(`/whatsapp/conversations/${conversationId}/messages`, data),
    assignConversation: (conversationId, assignedTo) => 
      api.put(`/whatsapp/conversations/${conversationId}/assign`, { assignedTo }),
    updateStatus: (conversationId, status) => 
      api.put(`/whatsapp/conversations/${conversationId}/status`, { status }),
    markAsRead: (conversationId) => 
      api.post(`/whatsapp/conversations/${conversationId}/mark-read`),
    sendTemplate: (data) => api.post('/whatsapp/send-template', data),
  },

  // Upload methods
  upload: {
    productImages: (productId, files) => {
      const formData = new FormData();
      files.forEach(file => formData.append('images', file));
      return api.post(`/upload/product-images/${productId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },
    categoryImage: (categoryId, file) => {
      const formData = new FormData();
      formData.append('image', file);
      return api.post(`/upload/category-image/${categoryId}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },
    deleteProductImage: (imageId) => api.delete(`/upload/product-image/${imageId}`),
    setPrimaryImage: (imageId) => api.patch(`/upload/product-image/${imageId}/primary`),
    reorderImages: (productId, imageIds) => 
      api.put(`/upload/product-images/${productId}/reorder`, { imageIds }),
  },

  // Analytics
  analytics: {
    getDashboard: () => api.get('/analytics/dashboard'),
    getProducts: (params = {}) => api.get('/analytics/products', { params }),
    getCustomers: (params = {}) => api.get('/analytics/customers', { params }),
    getProjects: (params = {}) => api.get('/analytics/projects', { params }),
    getWhatsApp: (params = {}) => api.get('/analytics/whatsapp', { params }),
  },
};

export default api;

// src/hooks/usePermissions.js
import { useAuth } from '../context/AuthContext';

export const usePermissions = () => {
  const { hasPermission, user } = useAuth();

  const can = {
    // Product permissions
    viewProducts: () => hasPermission('read:products') || user?.user_type !== 'customer',
    createProducts: () => hasPermission('write:products'),
    editProducts: () => hasPermission('write:products'),
    deleteProducts: () => hasPermission('write:products'),

    // Category permissions
    viewCategories: () => hasPermission('read:categories') || user?.user_type !== 'customer',
    createCategories: () => hasPermission('write:categories'),
    editCategories: () => hasPermission('write:categories'),
    deleteCategories: () => hasPermission('write:categories'),

    // Customer permissions
    viewCustomers: () => hasPermission('read:customers'),
    editCustomers: () => hasPermission('write:customers'),

    // Project permissions
    viewProjects: () => hasPermission('read:projects') || user?.user_type !== 'customer',
    createProjects: () => hasPermission('write:projects'),
    editProjects: () => hasPermission('write:projects'),

    // WhatsApp permissions
    viewWhatsApp: () => hasPermission('read:whatsapp'),
    manageWhatsApp: () => hasPermission('write:whatsapp'),

    // User management
    manageUsers: () => hasPermission('manage:users'),

    // Settings
    viewSettings: () => hasPermission('read:settings'),
    editSettings: () => hasPermission('write:settings'),

    // Analytics
    viewAnalytics: () => hasPermission('read:analytics'),
  };

  return { can, hasPermission };
};