// src/components/layout/Sidebar.jsx
import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { usePermissions } from '../../hooks/usePermissions';
import {
  HomeIcon,
  CubeIcon,
  TagIcon,
  UserGroupIcon,
  BriefcaseIcon,
  ChatBubbleLeftRightIcon,
  ChartBarIcon,
  CogIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@heroicons/react/24/outline';

const Sidebar = () => {
  const location = useLocation();
  const { user, getFullName } = useAuth();
  const { can } = usePermissions();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const navigation = [
    {
      name: 'Dashboard',
      href: '/',
      icon: HomeIcon,
      show: true
    },
    {
      name: 'Products',
      href: '/products',
      icon: CubeIcon,
      show: can.viewProducts()
    },
    {
      name: 'Categories',
      href: '/categories',
      icon: TagIcon,
      show: can.viewCategories()
    },
    {
      name: 'Customers',
      href: '/customers',
      icon: UserGroupIcon,
      show: can.viewCustomers()
    },
    {
      name: 'Projects',
      href: '/projects',
      icon: BriefcaseIcon,
      show: can.viewProjects()
    },
    {
      name: 'WhatsApp',
      href: '/whatsapp',
      icon: ChatBubbleLeftRightIcon,
      show: can.viewWhatsApp()
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: ChartBarIcon,
      show: can.viewAnalytics()
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: CogIcon,
      show: can.viewSettings()
    },
  ].filter(item => item.show);

  const isActivePath = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <div className={`bg-gray-900 text-white transition-all duration-300 ${isCollapsed ? 'w-16' : 'w-64'}`}>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className={`flex items-center ${isCollapsed ? 'justify-center' : ''}`}>
            <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">IC</span>
            </div>
            {!isCollapsed && (
              <span className="ml-2 text-lg font-bold">Portal</span>
            )}
          </div>
          
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1 rounded-lg hover:bg-gray-700 transition-colors duration-200"
          >
            {isCollapsed ? (
              <ChevronRightIcon className="h-5 w-5" />
            ) : (
              <ChevronLeftIcon className="h-5 w-5" />
            )}
          </button>
        </div>

        {/* User Info */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center">
              {user?.avatar_url ? (
                <img
                  src={user.avatar_url}
                  alt={getFullName()}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <span className="text-white font-medium">
                  {user?.first_name?.charAt(0) || 'U'}
                </span>
              )}
            </div>
            {!isCollapsed && (
              <div className="ml-3">
                <div className="text-sm font-medium">{getFullName()}</div>
                <div className="text-xs text-gray-400 capitalize">{user?.user_type}</div>
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-4 space-y-2">
          {navigation.map((item) => {
            const Icon = item.icon;
            const isActive = isActivePath(item.href);
            
            return (
              <Link
                key={item.name}
                to={item.href}
                className={`${
                  isActive
                    ? 'bg-indigo-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                } group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200`}
                title={isCollapsed ? item.name : undefined}
              >
                <Icon className="h-5 w-5 flex-shrink-0" />
                {!isCollapsed && (
                  <span className="ml-3">{item.name}</span>
                )}
              </Link>
            );
          })}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-gray-700">
          <div className={`text-xs text-gray-400 ${isCollapsed ? 'text-center' : ''}`}>
            {!isCollapsed && (
              <>
                <div>Interior Design Portal</div>
                <div>v1.0.0</div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;