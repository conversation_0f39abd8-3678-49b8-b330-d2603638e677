// src/components/common/ImageUpload.jsx
import React, { useState, useRef } from 'react';
import { FiUpload, FiX, FiImage, FiLoader } from 'react-icons/fi';
import uploadService from '../../services/uploadService';

const ImageUpload = ({
  onUpload,
  onError,
  multiple = false,
  maxFiles = 10,
  accept = "image/*",
  className = "",
  children,
  disabled = false
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [previews, setPreviews] = useState([]);
  const fileInputRef = useRef(null);

  const handleFileSelect = (files) => {
    try {
      const fileArray = Array.from(files);
      
      // Validate files
      if (multiple) {
        uploadService.validateFiles(fileArray);
      } else {
        uploadService.validateFile(fileArray[0]);
      }

      // Generate previews
      const newPreviews = fileArray.map(file => ({
        file,
        preview: uploadService.getFilePreview(file),
        name: file.name,
        size: uploadService.formatFileSize(file.size)
      }));

      setPreviews(multiple ? [...previews, ...newPreviews] : newPreviews);

      // Call onUpload callback
      if (onUpload) {
        onUpload(multiple ? fileArray : fileArray[0]);
      }
    } catch (error) {
      if (onError) {
        onError(error.message);
      }
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  };

  const handleInputChange = (e) => {
    const files = e.target.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  };

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const removePreview = (index) => {
    const newPreviews = [...previews];
    const removed = newPreviews.splice(index, 1)[0];
    
    // Clean up preview URL
    uploadService.cleanupFilePreview(removed.preview);
    
    setPreviews(newPreviews);
  };

  const clearPreviews = () => {
    previews.forEach(preview => {
      uploadService.cleanupFilePreview(preview.preview);
    });
    setPreviews([]);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleInputChange}
        className="hidden"
        disabled={disabled}
      />

      {/* Upload area */}
      <div
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${isDragging 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        {children || (
          <div className="space-y-2">
            <FiUpload className="w-8 h-8 text-gray-400 mx-auto" />
            <div className="text-sm text-gray-600">
              <span className="font-medium text-blue-600">Click to upload</span> or drag and drop
            </div>
            <div className="text-xs text-gray-500">
              {multiple ? `Up to ${maxFiles} images` : 'Single image'} (PNG, JPG, WebP, GIF up to 10MB)
            </div>
          </div>
        )}
      </div>

      {/* Preview area */}
      {previews.length > 0 && (
        <div className="mt-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">
              Selected Files ({previews.length})
            </h4>
            <button
              onClick={clearPreviews}
              className="text-xs text-red-600 hover:text-red-700"
            >
              Clear All
            </button>
          </div>
          
          <div className={`grid gap-3 ${
            multiple ? 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4' : 'grid-cols-1'
          }`}>
            {previews.map((preview, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  {preview.preview ? (
                    <img
                      src={preview.preview}
                      alt={preview.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <FiImage className="w-8 h-8 text-gray-400" />
                    </div>
                  )}
                </div>
                
                {/* Remove button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removePreview(index);
                  }}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <FiX className="w-4 h-4" />
                </button>
                
                {/* File info */}
                <div className="mt-1 text-xs text-gray-600 truncate">
                  <div className="font-medium truncate">{preview.name}</div>
                  <div className="text-gray-500">{preview.size}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload progress */}
      {isUploading && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <FiLoader className="w-4 h-4 text-blue-600 animate-spin" />
            <span className="text-sm text-blue-700">Uploading...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
