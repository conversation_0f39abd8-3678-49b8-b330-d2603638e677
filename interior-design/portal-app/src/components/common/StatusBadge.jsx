// src/components/common/StatusBadge.jsx
import React from 'react';

const StatusBadge = ({ status, type = 'default' }) => {
  const getStatusStyle = (status, type) => {
    const styles = {
      // Project statuses
      project: {
        inquiry: 'bg-yellow-100 text-yellow-800',
        consultation_scheduled: 'bg-blue-100 text-blue-800',
        in_progress: 'bg-indigo-100 text-indigo-800',
        design_ready: 'bg-purple-100 text-purple-800',
        approved: 'bg-green-100 text-green-800',
        execution: 'bg-orange-100 text-orange-800',
        completed: 'bg-green-100 text-green-800',
        cancelled: 'bg-red-100 text-red-800',
      },
      // Product statuses
      product: {
        active: 'bg-green-100 text-green-800',
        inactive: 'bg-gray-100 text-gray-800',
        in_stock: 'bg-green-100 text-green-800',
        out_of_stock: 'bg-red-100 text-red-800',
        on_order: 'bg-yellow-100 text-yellow-800',
      },
      // WhatsApp statuses
      whatsapp: {
        open: 'bg-green-100 text-green-800',
        in_progress: 'bg-blue-100 text-blue-800',
        resolved: 'bg-purple-100 text-purple-800',
        closed: 'bg-gray-100 text-gray-800',
      },
      // User statuses
      user: {
        active: 'bg-green-100 text-green-800',
        inactive: 'bg-red-100 text-red-800',
      },
      // Default
      default: {
        success: 'bg-green-100 text-green-800',
        warning: 'bg-yellow-100 text-yellow-800',
        error: 'bg-red-100 text-red-800',
        info: 'bg-blue-100 text-blue-800',
      }
    };

    return styles[type]?.[status] || styles.default.info;
  };

  const formatStatus = (status) => {
    return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusStyle(status, type)}`}>
      {formatStatus(status)}
    </span>
  );
};

export default StatusBadge;