# Server Configuration
NODE_ENV=development
PORT=5000
API_VERSION=v1

# Database Configuration (PostgreSQL on AWS RDS)
# DB_HOST=your-rds-endpoint.amazonaws.com
# DB_PORT=5432
# DB_NAME=interior_design_db
# DB_USERNAME=your_db_username
# DB_PASSWORD=your_db_password
# DB_SSL=true

DB_HOST=localhost
DB_PORT=5432
DB_NAME=klub_test_db
DB_USERNAME=postgres
DB_PASSWORD=postgres

# Auth0 Configuration
AUTH0_DOMAIN=dev-gmcycl8re2fgz0um.jp.auth0.com
AUTH0_AUDIENCE=https://your-api-identifier
AUTH0_CLIENT_ID=ZNEXGzDvesCDOJdG4zYElfub9XMav0ek
AUTH0_CLIENT_SECRET=RFxGZBLr3P72N5Y4TeXr86mKIPuB7oo6bRbLZx-4heYG7XJ9EvyxwOOCaT0-5B<PERSON><PERSON>
AUTH0_MANAGEMENT_AUDIENCE=https://dev-gmcycl8re2fgz0um.jp.auth0.com/api/v2/

# AWS Configuration
AWS_REGION=ap-south-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=1IJ1M0SPNkecS44rOws5b9zzYTNRk7x4a6ZFLUjB
S3_BUCKET_NAME=sd-interior-design
S3_BUCKET_URL=https://sd-interior-design.s3.ap-south-1.amazonaws.com


# WhatsApp Business API Configuration
WHATSAPP_TOKEN=your_whatsapp_business_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token

# Application Configuration
FRONTEND_URL=http://localhost:3000
PORTAL_URL=http://localhost:3001
API_BASE_URL=http://localhost:5000/api/v1

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp,image/gif
ALLOWED_DOCUMENT_TYPES=application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE_NAME=app.log

# Security
BCRYPT_ROUNDS=12
CORS_ORIGINS=http://localhost:3000,http://localhost:3001