const { DataTypes } = require("sequelize");
const { sequelize } = require("../database/connection");

const Wishlist = sequelize.define(
  "Wishlist",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: "users",
        key: "id",
      },
      onDelete: "CASCADE",
    },
    product_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: "products",
        key: "id",
      },
      onDelete: "CASCADE",
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: "wishlists",
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ["customer_id", "product_id"],
      },
      {
        fields: ["customer_id"],
      },
      {
        fields: ["product_id"],
      },
    ],
  }
);

// Instance methods
Wishlist.prototype.toSafeJSON = function () {
  const wishlist = this.toJSON();
  return {
    id: wishlist.id,
    customer_id: wishlist.customer_id,
    product_id: wishlist.product_id,
    created_at: wishlist.created_at,
    product: wishlist.Product ? wishlist.Product.toSafeJSON() : null,
  };
};

// Static methods
Wishlist.findByCustomer = async function (customerId, options = {}) {
  const { Product, ProductImage } = require("./index");

  return await this.findAll({
    where: { customer_id: customerId },
    include: [
      {
        model: Product,
        as: "product",
        include: [
          {
            model: ProductImage,
            as: "images",
            where: { is_primary: true },
            required: false,
          },
        ],
      },
    ],
    order: [["created_at", "DESC"]],
    ...options,
  });
};

Wishlist.addItem = async function (customerId, productId) {
  const [wishlistItem, created] = await this.findOrCreate({
    where: {
      customer_id: customerId,
      product_id: productId,
    },
    defaults: {
      customer_id: customerId,
      product_id: productId,
    },
  });

  return { wishlistItem, created };
};

Wishlist.removeItem = async function (customerId, productId) {
  return await this.destroy({
    where: {
      customer_id: customerId,
      product_id: productId,
    },
  });
};

Wishlist.isInWishlist = async function (customerId, productId) {
  const item = await this.findOne({
    where: {
      customer_id: customerId,
      product_id: productId,
    },
  });
  return !!item;
};

Wishlist.getCount = async function (customerId) {
  return await this.count({
    where: { customer_id: customerId },
  });
};

Wishlist.clearWishlist = async function (customerId) {
  return await this.destroy({
    where: { customer_id: customerId },
  });
};

// Define associations
Wishlist.associate = function (models) {
  // Wishlist belongs to User (customer)
  Wishlist.belongsTo(models.User, {
    foreignKey: "customer_id",
    as: "customer",
  });

  // Wishlist belongs to Product
  Wishlist.belongsTo(models.Product, {
    foreignKey: "product_id",
    as: "product",
  });
};

module.exports = Wishlist;
