const { DataTypes } = require('sequelize');
const { sequelize } = require('../database/connection');

// WhatsApp Conversation Model
const WhatsAppConversation = sequelize.define('WhatsAppConversation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  customer_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  whatsapp_number: {
    type: DataTypes.STRING(20),
    allowNull: false,
    validate: {
      is: /^[\+]?[1-9][\d]{0,15}$/
    }
  },
  customer_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  inquiry_type: {
    type: DataTypes.ENUM('general', 'product', 'consultation', 'support'),
    defaultValue: 'general'
  },
  status: {
    type: DataTypes.ENUM('open', 'in_progress', 'resolved', 'closed'),
    defaultValue: 'open'
  },
  assigned_to: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  project_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'projects',
      key: 'id'
    }
  },
  last_message_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Additional conversation metadata like tags, notes, etc.'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'whatsapp_conversations',
  indexes: [
    {
      fields: ['customer_id']
    },
    {
      fields: ['whatsapp_number']
    },
    {
      fields: ['status']
    },
    {
      fields: ['assigned_to']
    },
    {
      fields: ['inquiry_type']
    },
    {
      fields: ['last_message_at']
    }
  ],
  hooks: {
    beforeUpdate: (conversation) => {
      conversation.updated_at = new Date();
    }
  }
});

// WhatsApp Message Model
const WhatsAppMessage = sequelize.define('WhatsAppMessage', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  conversation_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'whatsapp_conversations',
      key: 'id'
    }
  },
  whatsapp_message_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    unique: true,
    comment: 'WhatsApp Business API message ID'
  },
  message_type: {
    type: DataTypes.ENUM('text', 'image', 'document', 'audio', 'video', 'template'),
    allowNull: false,
    defaultValue: 'text'
  },
  message_content: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  media_url: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'S3 URL for media files'
  },
  media_caption: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  sender_type: {
    type: DataTypes.ENUM('customer', 'staff'),
    allowNull: false
  },
  sender_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  sender_name: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  is_read: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  delivery_status: {
    type: DataTypes.ENUM('sent', 'delivered', 'read', 'failed'),
    defaultValue: 'sent'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  template_name: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'WhatsApp template name if message_type is template'
  },
  template_params: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Template parameters'
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Additional message metadata'
  },
  sent_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'whatsapp_messages',
  timestamps: false,
  indexes: [
    {
      fields: ['conversation_id']
    },
    {
      unique: true,
      fields: ['whatsapp_message_id'],
      where: {
        whatsapp_message_id: {
          [sequelize.Sequelize.Op.ne]: null
        }
      }
    },
    {
      fields: ['sender_type']
    },
    {
      fields: ['sender_id']
    },
    {
      fields: ['is_read']
    },
    {
      fields: ['delivery_status']
    },
    {
      fields: ['sent_at']
    }
  ]
});

// Define associations
WhatsAppConversation.associate = (models) => {
  // Customer association
  WhatsAppConversation.belongsTo(models.User, {
    foreignKey: 'customer_id',
    as: 'customer'
  });
  
  // Assigned staff association
  WhatsAppConversation.belongsTo(models.User, {
    foreignKey: 'assigned_to',
    as: 'assignedStaff'
  });
  
  // Project association
  WhatsAppConversation.belongsTo(models.Project, {
    foreignKey: 'project_id',
    as: 'project'
  });
  
  // Messages association
  WhatsAppConversation.hasMany(models.WhatsAppMessage, {
    foreignKey: 'conversation_id',
    as: 'messages'
  });
};

WhatsAppMessage.associate = (models) => {
  // Conversation association
  WhatsAppMessage.belongsTo(models.WhatsAppConversation, {
    foreignKey: 'conversation_id',
    as: 'conversation'
  });
  
  // Sender association
  WhatsAppMessage.belongsTo(models.User, {
    foreignKey: 'sender_id',
    as: 'sender'
  });
};

// Instance methods for WhatsAppConversation
WhatsAppConversation.prototype.updateLastMessage = async function(messageTime = new Date()) {
  return await this.update({ last_message_at: messageTime });
};

WhatsAppConversation.prototype.markAsRead = async function() {
  await WhatsAppMessage.update(
    { is_read: true },
    { where: { conversation_id: this.id, is_read: false } }
  );
};

WhatsAppConversation.prototype.getUnreadCount = async function() {
  return await WhatsAppMessage.count({
    where: { 
      conversation_id: this.id, 
      is_read: false,
      sender_type: 'customer'
    }
  });
};

WhatsAppConversation.prototype.getLastMessage = async function() {
  return await WhatsAppMessage.findOne({
    where: { conversation_id: this.id },
    order: [['sent_at', 'DESC']]
  });
};

// Class methods for WhatsAppConversation
WhatsAppConversation.findByPhoneNumber = async function(phoneNumber) {
  return await this.findOne({
    where: { whatsapp_number: phoneNumber },
    include: [
      {
        model: require('./User'),
        as: 'customer'
      },
      {
        model: require('./User'),
        as: 'assignedStaff'
      }
    ],
    order: [['last_message_at', 'DESC']]
  });
};

WhatsAppConversation.findActiveConversations = async function(limit = 50) {
  return await this.findAll({
    where: { 
      status: ['open', 'in_progress']
    },
    include: [
      {
        model: require('./User'),
        as: 'customer'
      },
      {
        model: require('./User'),
        as: 'assignedStaff'
      }
    ],
    order: [['last_message_at', 'DESC']],
    limit
  });
};

// Instance methods for WhatsAppMessage
WhatsAppMessage.prototype.markAsRead = async function() {
  return await this.update({ is_read: true });
};

WhatsAppMessage.prototype.updateDeliveryStatus = async function(status, errorMessage = null) {
  return await this.update({ 
    delivery_status: status,
    error_message: errorMessage
  });
};

// Class methods for WhatsAppMessage
WhatsAppMessage.findByConversation = async function(conversationId, limit = 50, offset = 0) {
  return await this.findAndCountAll({
    where: { conversation_id: conversationId },
    include: [
      {
        model: require('./User'),
        as: 'sender',
        attributes: ['id', 'first_name', 'last_name', 'user_type']
      }
    ],
    order: [['sent_at', 'DESC']],
    limit,
    offset
  });
};

module.exports = {
  WhatsAppConversation,
  WhatsAppMessage
};