const { sequelize } = require('../database/connection');

// Import all models
const User = require('./User');
const Category = require('./Category');
const Product = require('./Product');
const ProductImage = require('./ProductImage');
const { WhatsAppConversation, WhatsAppMessage } = require('./WhatsApp');

// Create models object
const models = {
  User,
  Category,
  Product,
  ProductImage,
  WhatsAppConversation,
  WhatsAppMessage
};

// Set up associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Add sequelize instance and Sequelize class to models
models.sequelize = sequelize;
models.Sequelize = require('sequelize');

module.exports = models