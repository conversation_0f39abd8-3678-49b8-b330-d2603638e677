const { DataTypes } = require('sequelize');
const { sequelize } = require('../database/connection');

const Project = sequelize.define('Project', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  customer_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  designer_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  sales_person_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  project_name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 255]
    }
  },
  project_type: {
    type: DataTypes.ENUM('consultation', 'full_design', 'partial_design', 'product_only'),
    allowNull: true
  },
  property_type: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  property_size: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  number_of_rooms: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0
    }
  },
  budget_min: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  budget_max: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  preferred_style: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  timeline: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('inquiry', 'consultation_scheduled', 'in_progress', 'design_ready', 'approved', 'execution', 'completed', 'cancelled'),
    defaultValue: 'inquiry'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  start_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  end_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  completion_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'projects',
  indexes: [
    {
      fields: ['customer_id']
    },
    {
      fields: ['designer_id']
    },
    {
      fields: ['sales_person_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['created_at']
    }
  ],
  hooks: {
    beforeUpdate: (project) => {
      project.updated_at = new Date();
    }
  }
});

// Define associations
Project.associate = (models) => {
  // Customer association
  Project.belongsTo(models.User, {
    foreignKey: 'customer_id',
    as: 'customer'
  });
  
  // Designer association
  Project.belongsTo(models.User, {
    foreignKey: 'designer_id',
    as: 'designer'
  });
  
  // Sales person association
  Project.belongsTo(models.User, {
    foreignKey: 'sales_person_id',
    as: 'salesPerson'
  });
  
  // WhatsApp conversations association
  Project.hasMany(models.WhatsAppConversation, {
    foreignKey: 'project_id',
    as: 'whatsappConversations'
  });
  
  // TODO: Add other associations when models are created
  // Project.hasMany(models.ProjectFile, {
  //   foreignKey: 'project_id',
  //   as: 'files'
  // });
  
  // Project.hasMany(models.ProductReview, {
  //   foreignKey: 'project_id',
  //   as: 'reviews'
  // });
};

// Instance methods
Project.prototype.getStatusLabel = function() {
  const statusLabels = {
    'inquiry': 'Inquiry',
    'consultation_scheduled': 'Consultation Scheduled',
    'in_progress': 'In Progress',
    'design_ready': 'Design Ready',
    'approved': 'Approved',
    'execution': 'Execution',
    'completed': 'Completed',
    'cancelled': 'Cancelled'
  };
  return statusLabels[this.status] || this.status;
};

Project.prototype.getPriorityLabel = function() {
  const priorityLabels = {
    'low': 'Low',
    'medium': 'Medium',
    'high': 'High',
    'urgent': 'Urgent'
  };
  return priorityLabels[this.priority] || this.priority;
};

Project.prototype.getBudgetRange = function() {
  if (this.budget_min && this.budget_max) {
    return `₹${this.budget_min} - ₹${this.budget_max}`;
  } else if (this.budget_min) {
    return `₹${this.budget_min}+`;
  } else if (this.budget_max) {
    return `Up to ₹${this.budget_max}`;
  }
  return 'Not specified';
};

Project.prototype.isActive = function() {
  return !['completed', 'cancelled'].includes(this.status);
};

Project.prototype.toSafeJSON = function() {
  const project = this.toJSON();
  return project;
};

module.exports = Project;
