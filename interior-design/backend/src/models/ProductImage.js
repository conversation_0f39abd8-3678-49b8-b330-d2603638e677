const { DataTypes } = require('sequelize');
const { sequelize } = require('../database/connection');

const ProductImage = sequelize.define('ProductImage', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  product_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'products',
      key: 'id'
    }
  },
  image_url: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: 'S3 URL for the image'
  },
  alt_text: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  is_primary: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  file_name: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Original filename'
  },
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'File size in bytes'
  },
  mime_type: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  width: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  height: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'product_images',
  timestamps: false,
  indexes: [
    {
      fields: ['product_id']
    },
    {
      fields: ['is_primary']
    },
    {
      fields: ['sort_order']
    }
  ],
  hooks: {
    beforeCreate: async (productImage) => {
      // If this is being set as primary, unset other primary images for the same product
      if (productImage.is_primary) {
        await ProductImage.update(
          { is_primary: false },
          { where: { product_id: productImage.product_id, is_primary: true } }
        );
      }
    },
    beforeUpdate: async (productImage) => {
      // If this is being set as primary, unset other primary images for the same product
      if (productImage.is_primary && productImage.changed('is_primary')) {
        await ProductImage.update(
          { is_primary: false },
          { 
            where: { 
              product_id: productImage.product_id, 
              is_primary: true,
              id: { [sequelize.Sequelize.Op.ne]: productImage.id }
            } 
          }
        );
      }
    }
  }
});

// Define associations
ProductImage.associate = (models) => {
  ProductImage.belongsTo(models.Product, {
    foreignKey: 'product_id',
    as: 'product'
  });
};

// Instance methods
ProductImage.prototype.getImageUrl = function(size = 'original') {
  // Return different image sizes based on parameter
  const baseUrl = this.image_url;
  
  switch (size) {
    case 'thumbnail':
      return baseUrl.replace(/(\.[^.]+)$/, '_thumb$1');
    case 'medium':
      return baseUrl.replace(/(\.[^.]+)$/, '_medium$1');
    case 'large':
      return baseUrl.replace(/(\.[^.]+)$/, '_large$1');
    default:
      return baseUrl;
  }
};

// Class methods
ProductImage.findByProduct = async function(productId) {
  return await this.findAll({
    where: { product_id: productId },
    order: [['is_primary', 'DESC'], ['sort_order', 'ASC'], ['created_at', 'ASC']]
  });
};

ProductImage.findPrimaryByProduct = async function(productId) {
  return await this.findOne({
    where: { product_id: productId, is_primary: true }
  });
};

module.exports = ProductImage;