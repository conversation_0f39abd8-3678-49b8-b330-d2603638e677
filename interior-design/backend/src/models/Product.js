const { DataTypes } = require('sequelize');
const { sequelize } = require('../database/connection');

const Product = sequelize.define('Product', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 255]
    }
  },
  slug: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      is: /^[a-z0-9-]+$/
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  short_description: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  sku: {
    type: DataTypes.STRING(100),
    allowNull: true,
    unique: true
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  discount_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  category_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  brand: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  material: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  dimensions: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Format: L x W x H (in cm or inches)'
  },
  color: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  style: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  room_type: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'Living Room, Bedroom, Kitchen, etc.'
  },
  is_featured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  stock_status: {
    type: DataTypes.ENUM('in_stock', 'out_of_stock', 'on_order'),
    defaultValue: 'in_stock'
  },
  weight: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: 'Weight in kg'
  },
  warranty_period: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'e.g., "2 years", "1 year"'
  },
  assembly_required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  meta_title: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  meta_description: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  view_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  created_by: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'products',
  indexes: [
    {
      unique: true,
      fields: ['slug']
    },
    {
      unique: true,
      fields: ['sku'],
      where: {
        sku: {
          [sequelize.Sequelize.Op.ne]: null
        }
      }
    },
    {
      fields: ['category_id']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['is_featured']
    },
    {
      fields: ['stock_status']
    },
    {
      fields: ['brand']
    },
    {
      fields: ['room_type']
    },
    {
      fields: ['price']
    }
  ],
  hooks: {
    beforeCreate: (product) => {
      if (!product.slug && product.name) {
        product.slug = product.name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim('-');
      }
      
      // Generate SKU if not provided
      if (!product.sku) {
        const timestamp = Date.now().toString().slice(-6);
        const randomStr = Math.random().toString(36).substring(2, 5).toUpperCase();
        product.sku = `PRD-${timestamp}-${randomStr}`;
      }
    },
    beforeUpdate: (product) => {
      product.updated_at = new Date();
      if (product.changed('name') && !product.changed('slug')) {
        product.slug = product.name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim('-');
      }
    }
  }
});

// Define associations
Product.associate = (models) => {
  // Category association
  Product.belongsTo(models.Category, {
    foreignKey: 'category_id',
    as: 'category'
  });
  
  // User association (created_by)
  Product.belongsTo(models.User, {
    foreignKey: 'created_by',
    as: 'creator'
  });
  
  // Product images association
  Product.hasMany(models.ProductImage, {
    foreignKey: 'product_id',
    as: 'images'
  });
  
  // Product variants association
  Product.hasMany(models.ProductVariant, {
    foreignKey: 'product_id',
    as: 'variants'
  });
  
  // Wishlists association
  Product.hasMany(models.Wishlist, {
    foreignKey: 'product_id',
    as: 'wishlists'
  });
  
  // Reviews association
  Product.hasMany(models.ProductReview, {
    foreignKey: 'product_id',
    as: 'reviews'
  });
};

// Instance methods
Product.prototype.getDiscountPercentage = function() {
  if (this.price && this.discount_price && this.discount_price < this.price) {
    return Math.round(((this.price - this.discount_price) / this.price) * 100);
  }
  return 0;
};

Product.prototype.getFinalPrice = function() {
  return this.discount_price && this.discount_price < this.price 
    ? this.discount_price 
    : this.price;
};

Product.prototype.isInStock = function() {
  return this.stock_status === 'in_stock';
};

Product.prototype.incrementViewCount = async function() {
  return await this.increment('view_count');
};

Product.prototype.toSafeJSON = function() {
  const product = this.toJSON();
  return product;
};

// Class methods
Product.findBySlug = async function(slug, includeImages = true) {
  const include = [
    {
      model: require('./Category'),
      as: 'category'
    }
  ];
  
  if (includeImages) {
    include.push({
      model: require('./ProductImage'),
      as: 'images',
      order: [['sort_order', 'ASC'], ['created_at', 'ASC']]
    });
  }
  
  return await this.findOne({
    where: { slug, is_active: true },
    include
  });
};

Product.findFeatured = async function(limit = 10) {
  return await this.findAll({
    where: { 
      is_featured: true, 
      is_active: true 
    },
    include: [
      {
        model: require('./Category'),
        as: 'category'
      },
      {
        model: require('./ProductImage'),
        as: 'images',
        where: { is_primary: true },
        required: false
      }
    ],
    limit,
    order: [['sort_order', 'ASC'], ['created_at', 'DESC']]
  });
};

Product.findByCategory = async function(categoryId, options = {}) {
  const { page = 1, limit = 20, sortBy = 'created_at', sortOrder = 'DESC' } = options;
  const offset = (page - 1) * limit;
  
  return await this.findAndCountAll({
    where: { 
      category_id: categoryId,
      is_active: true 
    },
    include: [
      {
        model: require('./Category'),
        as: 'category'
      },
      {
        model: require('./ProductImage'),
        as: 'images',
        where: { is_primary: true },
        required: false
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [[sortBy, sortOrder]]
  });
};

Product.search = async function(query, options = {}) {
  const { page = 1, limit = 20, categoryId, minPrice, maxPrice, brand, roomType } = options;
  const offset = (page - 1) * limit;
  const { Op } = require('sequelize');
  
  const whereClause = {
    is_active: true,
    [Op.or]: [
      { name: { [Op.iLike]: `%${query}%` } },
      { description: { [Op.iLike]: `%${query}%` } },
      { short_description: { [Op.iLike]: `%${query}%` } },
      { brand: { [Op.iLike]: `%${query}%` } },
      { material: { [Op.iLike]: `%${query}%` } },
      { style: { [Op.iLike]: `%${query}%` } }
    ]
  };
  
  // Apply filters
  if (categoryId) {
    whereClause.category_id = categoryId;
  }
  
  if (minPrice || maxPrice) {
    whereClause.price = {};
    if (minPrice) whereClause.price[Op.gte] = minPrice;
    if (maxPrice) whereClause.price[Op.lte] = maxPrice;
  }
  
  if (brand) {
    whereClause.brand = { [Op.iLike]: `%${brand}%` };
  }
  
  if (roomType) {
    whereClause.room_type = { [Op.iLike]: `%${roomType}%` };
  }
  
  return await this.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: require('./Category'),
        as: 'category'
      },
      {
        model: require('./ProductImage'),
        as: 'images',
        where: { is_primary: true },
        required: false
      }
    ],
    limit: parseInt(limit),
    offset: parseInt(offset),
    order: [['created_at', 'DESC']]
  });
};

Product.getFilters = async function() {
  const filters = await this.findAll({
    where: { is_active: true },
    attributes: [
      [sequelize.fn('DISTINCT', sequelize.col('brand')), 'brand'],
      [sequelize.fn('DISTINCT', sequelize.col('room_type')), 'room_type'],
      [sequelize.fn('DISTINCT', sequelize.col('style')), 'style'],
      [sequelize.fn('DISTINCT', sequelize.col('material')), 'material'],
      [sequelize.fn('MIN', sequelize.col('price')), 'min_price'],
      [sequelize.fn('MAX', sequelize.col('price')), 'max_price']
    ],
    raw: true
  });
  
  return filters;
};

module.exports = Product;