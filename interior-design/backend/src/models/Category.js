const { DataTypes } = require('sequelize');
const { sequelize } = require('../database/connection');

const Category = sequelize.define('Category', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 100]
    }
  },
  slug: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true,
      is: /^[a-z0-9-]+$/
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  parent_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  image_url: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'Category image stored in S3'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  meta_title: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  meta_description: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'categories',
  indexes: [
    {
      unique: true,
      fields: ['slug']
    },
    {
      fields: ['parent_id']
    },
    {
      fields: ['is_active']
    },
    {
      fields: ['sort_order']
    }
  ],
  hooks: {
    beforeCreate: (category) => {
      if (!category.slug && category.name) {
        category.slug = category.name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim('-');
      }
    },
    beforeUpdate: (category) => {
      category.updated_at = new Date();
      if (category.changed('name') && !category.changed('slug')) {
        category.slug = category.name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim('-');
      }
    }
  }
});

// Define associations
Category.associate = (models) => {
  // Self-referencing association for parent-child categories
  Category.belongsTo(models.Category, {
    foreignKey: 'parent_id',
    as: 'parent'
  });
  
  Category.hasMany(models.Category, {
    foreignKey: 'parent_id',
    as: 'children'
  });
  
  // Association with products
  Category.hasMany(models.Product, {
    foreignKey: 'category_id',
    as: 'products'
  });
};

// Instance methods
Category.prototype.getFullPath = function() {
  // Returns the full category path (e.g., "Furniture > Living Room > Sofas")
  let path = [this.name];
  let current = this;
  
  while (current.parent) {
    path.unshift(current.parent.name);
    current = current.parent;
  }
  
  return path.join(' > ');
};

Category.prototype.toSafeJSON = function() {
  const category = this.toJSON();
  return category;
};

// Class methods
Category.findBySlug = async function(slug) {
  return await this.findOne({ 
    where: { slug, is_active: true },
    include: [
      {
        model: Category,
        as: 'parent'
      },
      {
        model: Category,
        as: 'children',
        where: { is_active: true },
        required: false
      }
    ]
  });
};

Category.findRootCategories = async function() {
  return await this.findAll({
    where: { 
      parent_id: null, 
      is_active: true 
    },
    include: [
      {
        model: Category,
        as: 'children',
        where: { is_active: true },
        required: false,
        include: [
          {
            model: Category,
            as: 'children',
            where: { is_active: true },
            required: false
          }
        ]
      }
    ],
    order: [
      ['sort_order', 'ASC'],
      ['name', 'ASC'],
      [{ model: Category, as: 'children' }, 'sort_order', 'ASC'],
      [{ model: Category, as: 'children' }, 'name', 'ASC']
    ]
  });
};

Category.findWithProductCount = async function() {
  const { Product } = require('./Product');
  
  return await this.findAll({
    where: { is_active: true },
    include: [
      {
        model: Product,
        as: 'products',
        attributes: [],
        where: { is_active: true },
        required: false
      }
    ],
    attributes: [
      'id',
      'name',
      'slug',
      'description',
      'image_url',
      'parent_id',
      [sequelize.fn('COUNT', sequelize.col('products.id')), 'product_count']
    ],
    group: ['Category.id'],
    order: [['sort_order', 'ASC'], ['name', 'ASC']]
  });
};

Category.buildCategoryTree = async function() {
  const categories = await this.findAll({
    where: { is_active: true },
    order: [['sort_order', 'ASC'], ['name', 'ASC']]
  });
  
  const categoryMap = {};
  const tree = [];
  
  // Create a map of all categories
  categories.forEach(category => {
    categoryMap[category.id] = {
      ...category.toJSON(),
      children: []
    };
  });
  
  // Build the tree structure
  categories.forEach(category => {
    if (category.parent_id) {
      if (categoryMap[category.parent_id]) {
        categoryMap[category.parent_id].children.push(categoryMap[category.id]);
      }
    } else {
      tree.push(categoryMap[category.id]);
    }
  });
  
  return tree;
};

module.exports = Category;