const { DataTypes } = require('sequelize');
const { sequelize } = require('../database/connection');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  auth0_id: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    comment: 'Auth0 user identifier'
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  first_name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  last_name: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      is: /^[\+]?[1-9][\d]{0,15}$/
    }
  },
  user_type: {
    type: DataTypes.ENUM('customer', 'admin', 'designer', 'sales'),
    allowNull: false,
    defaultValue: 'customer'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  email_verified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  avatar_url: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: 'URL to user avatar image in S3'
  },
  timezone: {
    type: DataTypes.STRING(50),
    defaultValue: 'Asia/Kolkata'
  },
  language: {
    type: DataTypes.STRING(10),
    defaultValue: 'en'
  },
  last_login_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'users',
  indexes: [
    {
      unique: true,
      fields: ['auth0_id']
    },
    {
      unique: true,
      fields: ['email']
    },
    {
      fields: ['user_type']
    },
    {
      fields: ['is_active']
    }
  ],
  hooks: {
    beforeUpdate: (user) => {
      user.updated_at = new Date();
    }
  }
});

// Instance methods
User.prototype.getFullName = function() {
  return `${this.first_name} ${this.last_name || ''}`.trim();
};

User.prototype.isCustomer = function() {
  return this.user_type === 'customer';
};

User.prototype.isStaff = function() {
  return ['admin', 'designer', 'sales'].includes(this.user_type);
};

User.prototype.isAdmin = function() {
  return this.user_type === 'admin';
};

User.prototype.canAccessPortal = function() {
  return this.isStaff();
};

User.prototype.toSafeJSON = function() {
  const user = this.toJSON();
  delete user.auth0_id; // Don't expose Auth0 ID to frontend
  return user;
};

// Class methods
User.findByAuth0Id = async function(auth0Id) {
  return await this.findOne({ where: { auth0_id: auth0Id } });
};

User.findByEmail = async function(email) {
  return await this.findOne({ where: { email: email.toLowerCase() } });
};

User.findStaffMembers = async function() {
  return await this.findAll({
    where: {
      user_type: ['admin', 'designer', 'sales'],
      is_active: true
    },
    order: [['created_at', 'DESC']]
  });
};

User.findCustomers = async function(limit = 50, offset = 0) {
  return await this.findAndCountAll({
    where: {
      user_type: 'customer',
      is_active: true
    },
    limit,
    offset,
    order: [['created_at', 'DESC']]
  });
};

// Update last login timestamp
User.updateLastLogin = async function(userId) {
  return await this.update(
    { last_login_at: new Date() },
    { where: { id: userId } }
  );
};

module.exports = User;