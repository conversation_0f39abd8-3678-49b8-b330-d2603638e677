const AWS = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const sharp = require('sharp');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const logger = require('../utils/logger');

// Configure AWS SDK
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || 'ap-south-1'
});

const s3 = new AWS.S3();

// S3 bucket configuration
const BUCKET_NAME = process.env.S3_BUCKET_NAME;
const BUCKET_URL = process.env.S3_BUCKET_URL;

// File type configurations
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
const ALLOWED_DOCUMENT_TYPES = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024; // 10MB

// Helper function to generate unique filename
const generateFileName = (originalname, folder = 'uploads') => {
  const extension = path.extname(originalname);
  const filename = `${uuidv4()}${extension}`;
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  return `${folder}/${timestamp}/${filename}`;
};

// Helper function to get file type category
const getFileCategory = (mimetype) => {
  if (ALLOWED_IMAGE_TYPES.includes(mimetype)) {
    return 'image';
  } else if (ALLOWED_DOCUMENT_TYPES.includes(mimetype)) {
    return 'document';
  }
  return 'unknown';
};

// S3 upload configuration for different file types
const createS3Storage = (folder = 'uploads', allowedTypes = [...ALLOWED_IMAGE_TYPES, ...ALLOWED_DOCUMENT_TYPES]) => {
  return multerS3({
    s3: s3,
    bucket: BUCKET_NAME,
    acl: 'public-read',
    contentType: multerS3.AUTO_CONTENT_TYPE,
    key: (req, file, cb) => {
      const filename = generateFileName(file.originalname, folder);
      cb(null, filename);
    },
    fileFilter: (req, file, cb) => {
      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error(`File type ${file.mimetype} not allowed`), false);
      }
    }
  });
};

// Multer configurations for different upload types
const productImageUpload = multer({
  storage: createS3Storage('products/images', ALLOWED_IMAGE_TYPES),
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: 10 // Maximum 10 images per product
  },
  fileFilter: (req, file, cb) => {
    if (ALLOWED_IMAGE_TYPES.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

const categoryImageUpload = multer({
  storage: createS3Storage('categories/images', ALLOWED_IMAGE_TYPES),
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: 1
  },
  fileFilter: (req, file, cb) => {
    if (ALLOWED_IMAGE_TYPES.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

const projectDocumentUpload = multer({
  storage: createS3Storage('projects/documents', [...ALLOWED_IMAGE_TYPES, ...ALLOWED_DOCUMENT_TYPES]),
  limits: {
    fileSize: MAX_FILE_SIZE,
    files: 20
  }
});

const userAvatarUpload = multer({
  storage: createS3Storage('users/avatars', ALLOWED_IMAGE_TYPES),
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB for avatars
    files: 1
  },
  fileFilter: (req, file, cb) => {
    if (ALLOWED_IMAGE_TYPES.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// S3 service class
class S3Service {
  constructor() {
    this.s3 = s3;
    this.bucketName = BUCKET_NAME;
    this.bucketUrl = BUCKET_URL;
  }

  // Upload file directly to S3 (for programmatic uploads)
  async uploadFile(fileBuffer, filename, mimetype, folder = 'uploads') {
    try {
      const key = generateFileName(filename, folder);
      
      const params = {
        Bucket: this.bucketName,
        Key: key,
        Body: fileBuffer,
        ContentType: mimetype,
        ACL: 'public-read'
      };

      const result = await this.s3.upload(params).promise();
      
      logger.info('File uploaded to S3', {
        key: result.Key,
        location: result.Location,
        bucket: result.Bucket
      });

      return {
        key: result.Key,
        url: result.Location,
        bucket: result.Bucket
      };
    } catch (error) {
      logger.error('S3 upload error:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  // Upload and process image with different sizes
  async uploadImageWithSizes(fileBuffer, filename, folder = 'uploads') {
    try {
      const sizes = {
        thumbnail: { width: 150, height: 150 },
        medium: { width: 400, height: 400 },
        large: { width: 800, height: 800 }
      };

      const uploadPromises = [];
      const extension = path.extname(filename);
      const baseName = path.basename(filename, extension);

      // Upload original
      uploadPromises.push(
        this.uploadFile(fileBuffer, filename, 'image/jpeg', folder)
      );

      // Generate and upload different sizes
      for (const [sizeName, dimensions] of Object.entries(sizes)) {
        const resizedBuffer = await sharp(fileBuffer)
          .resize(dimensions.width, dimensions.height, {
            fit: 'cover',
            position: 'center'
          })
          .jpeg({ quality: 80 })
          .toBuffer();

        const sizedFilename = `${baseName}_${sizeName}${extension}`;
        uploadPromises.push(
          this.uploadFile(resizedBuffer, sizedFilename, 'image/jpeg', folder)
        );
      }

      const results = await Promise.all(uploadPromises);
      
      return {
        original: results[0],
        thumbnail: results[1],
        medium: results[2],
        large: results[3]
      };
    } catch (error) {
      logger.error('Image processing and upload error:', error);
      throw new Error(`Failed to process and upload image: ${error.message}`);
    }
  }

  // Delete file from S3
  async deleteFile(key) {
    try {
      const params = {
        Bucket: this.bucketName,
        Key: key
      };

      await this.s3.deleteObject(params).promise();
      
      logger.info('File deleted from S3', { key });
      return true;
    } catch (error) {
      logger.error('S3 delete error:', error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  // Delete multiple files from S3
  async deleteFiles(keys) {
    try {
      if (!keys || keys.length === 0) return true;

      const params = {
        Bucket: this.bucketName,
        Delete: {
          Objects: keys.map(key => ({ Key: key })),
          Quiet: false
        }
      };

      const result = await this.s3.deleteObjects(params).promise();
      
      logger.info('Multiple files deleted from S3', {
        deleted: result.Deleted?.length || 0,
        errors: result.Errors?.length || 0
      });

      return result;
    } catch (error) {
      logger.error('S3 bulk delete error:', error);
      throw new Error(`Failed to delete files: ${error.message}`);
    }
  }

  // Get signed URL for private files
  async getSignedUrl(key, expiresIn = 3600) {
    try {
      const params = {
        Bucket: this.bucketName,
        Key: key,
        Expires: expiresIn
      };

      const url = await this.s3.getSignedUrlPromise('getObject', params);
      return url;
    } catch (error) {
      logger.error('S3 signed URL error:', error);
      throw new Error(`Failed to generate signed URL: ${error.message}`);
    }
  }

  // Check if file exists
  async fileExists(key) {
    try {
      await this.s3.headObject({
        Bucket: this.bucketName,
        Key: key
      }).promise();
      return true;
    } catch (error) {
      if (error.code === 'NotFound') {
        return false;
      }
      throw error;
    }
  }

  // Get file metadata
  async getFileMetadata(key) {
    try {
      const result = await this.s3.headObject({
        Bucket: this.bucketName,
        Key: key
      }).promise();

      return {
        contentType: result.ContentType,
        contentLength: result.ContentLength,
        lastModified: result.LastModified,
        etag: result.ETag
      };
    } catch (error) {
      logger.error('S3 metadata error:', error);
      throw new Error(`Failed to get file metadata: ${error.message}`);
    }
  }

  // List files in a folder
  async listFiles(prefix = '', maxKeys = 1000) {
    try {
      const params = {
        Bucket: this.bucketName,
        Prefix: prefix,
        MaxKeys: maxKeys
      };

      const result = await this.s3.listObjectsV2(params).promise();
      
      return {
        files: result.Contents || [],
        isTruncated: result.IsTruncated,
        nextContinuationToken: result.NextContinuationToken
      };
    } catch (error) {
      logger.error('S3 list files error:', error);
      throw new Error(`Failed to list files: ${error.message}`);
    }
  }

  // Copy file within S3
  async copyFile(sourceKey, destinationKey) {
    try {
      const params = {
        Bucket: this.bucketName,
        CopySource: `${this.bucketName}/${sourceKey}`,
        Key: destinationKey,
        ACL: 'public-read'
      };

      const result = await this.s3.copyObject(params).promise();
      
      logger.info('File copied in S3', {
        source: sourceKey,
        destination: destinationKey
      });

      return result;
    } catch (error) {
      logger.error('S3 copy error:', error);
      throw new Error(`Failed to copy file: ${error.message}`);
    }
  }

  // Get public URL for a file
  getPublicUrl(key) {
    return `${this.bucketUrl}/${key}`;
  }

  // Extract key from S3 URL
  extractKeyFromUrl(url) {
    if (!url) return null;
    
    // Handle both bucket URL and full S3 URLs
    if (url.includes(this.bucketUrl)) {
      return url.replace(`${this.bucketUrl}/`, '');
    }
    
    // Handle s3://bucket/key format
    if (url.startsWith('s3://')) {
      const parts = url.replace('s3://', '').split('/');
      parts.shift(); // Remove bucket name
      return parts.join('/');
    }
    
    // Handle https://bucket.s3.region.amazonaws.com/key format
    const s3UrlPattern = /https:\/\/[^\/]+\.s3\.[^\/]+\.amazonaws\.com\/(.+)/;
    const match = url.match(s3UrlPattern);
    if (match) {
      return match[1];
    }
    
    return null;
  }
}

// Create singleton instance
const s3Service = new S3Service();

// Health check for S3 connection
const s3HealthCheck = async () => {
  try {
    await s3.headBucket({ Bucket: BUCKET_NAME }).promise();
    return { status: 'healthy', timestamp: new Date() };
  } catch (error) {
    return { 
      status: 'unhealthy', 
      error: error.message, 
      timestamp: new Date() 
    };
  }
};

module.exports = {
  s3Service,
  s3HealthCheck,
  // Multer upload configurations
  productImageUpload,
  categoryImageUpload,
  projectDocumentUpload,
  userAvatarUpload,
  // Constants
  ALLOWED_IMAGE_TYPES,
  ALLOWED_DOCUMENT_TYPES,
  MAX_FILE_SIZE
};