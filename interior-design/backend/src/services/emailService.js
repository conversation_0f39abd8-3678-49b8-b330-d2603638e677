const nodemailer = require('nodemailer');
const AWS = require('aws-sdk');
const logger = require('../utils/logger');

// Configure AWS SES
const ses = new AWS.SES({
  region: process.env.AWS_REGION || 'us-east-1',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

// Create nodemailer transporter using SES
const transporter = nodemailer.createTransporter({
  SES: { ses, aws: AWS }
});

class EmailService {
  constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.companyName = process.env.COMPANY_NAME || 'Interior Design Studio';
    this.frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
  }

  // Send welcome email to new users
  async sendWelcomeEmail(email, firstName) {
    try {
      const subject = `Welcome to ${this.companyName}!`;
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to ${this.companyName}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to ${this.companyName}!</h1>
            </div>
            <div class="content">
              <h2>Hello ${firstName}!</h2>
              <p>Thank you for joining our interior design community. We're excited to help you transform your space into something beautiful and functional.</p>
              
              <p>Here's what you can do with your new account:</p>
              <ul>
                <li>Browse our extensive product catalog</li>
                <li>Save your favorite items to your wishlist</li>
                <li>Request design consultations</li>
                <li>Track your projects and orders</li>
                <li>Connect with our design experts via WhatsApp</li>
              </ul>
              
              <div style="text-align: center;">
                <a href="${this.frontendUrl}/catalog" class="button">Start Browsing</a>
              </div>
              
              <p>If you have any questions, feel free to reach out to our support team or chat with us directly through WhatsApp.</p>
              
              <p>Best regards,<br>The ${this.companyName} Team</p>
            </div>
            <div class="footer">
              <p>© 2024 ${this.companyName}. All rights reserved.</p>
              <p>If you didn't create this account, please ignore this email.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      const mailOptions = {
        from: this.fromEmail,
        to: email,
        subject,
        html
      };

      await transporter.sendMail(mailOptions);
      logger.info(`Welcome email sent to ${email}`);

    } catch (error) {
      logger.error('Failed to send welcome email:', error);
      throw error;
    }
  }

  // Send password reset email
  async sendPasswordResetEmail(email, firstName, resetToken) {
    try {
      const resetUrl = `${this.frontendUrl}/reset-password?token=${resetToken}`;
      const subject = 'Password Reset Request';
      
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Password Reset - ${this.companyName}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc3545; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .button { display: inline-block; background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Password Reset Request</h1>
            </div>
            <div class="content">
              <h2>Hello ${firstName},</h2>
              <p>We received a request to reset your password for your ${this.companyName} account.</p>
              
              <div class="warning">
                <strong>Security Notice:</strong> This link will expire in 1 hour for your security.
              </div>
              
              <p>Click the button below to reset your password:</p>
              
              <div style="text-align: center;">
                <a href="${resetUrl}" class="button">Reset Password</a>
              </div>
              
              <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
              <p style="word-break: break-all; background: #f1f1f1; padding: 10px; border-radius: 5px;">${resetUrl}</p>
              
              <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
              
              <p>Best regards,<br>The ${this.companyName} Team</p>
            </div>
            <div class="footer">
              <p>© 2024 ${this.companyName}. All rights reserved.</p>
              <p>This is an automated email. Please do not reply to this message.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      const mailOptions = {
        from: this.fromEmail,
        to: email,
        subject,
        html
      };

      await transporter.sendMail(mailOptions);
      logger.info(`Password reset email sent to ${email}`);

    } catch (error) {
      logger.error('Failed to send password reset email:', error);
      throw error;
    }
  }

  // Send consultation request notification
  async sendConsultationRequestEmail(customerEmail, customerName, consultationDetails) {
    try {
      const subject = 'Consultation Request Received';
      
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Consultation Request - ${this.companyName}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .details { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Consultation Request Received</h1>
            </div>
            <div class="content">
              <h2>Hello ${customerName},</h2>
              <p>Thank you for your interest in our interior design services! We've received your consultation request and our team will review it shortly.</p>
              
              <div class="details">
                <h3>Your Request Details:</h3>
                <p><strong>Project Type:</strong> ${consultationDetails.projectType || 'Not specified'}</p>
                <p><strong>Property Type:</strong> ${consultationDetails.propertyType || 'Not specified'}</p>
                <p><strong>Budget Range:</strong> ${consultationDetails.budgetRange || 'Not specified'}</p>
                <p><strong>Timeline:</strong> ${consultationDetails.timeline || 'Not specified'}</p>
                <p><strong>Description:</strong> ${consultationDetails.description || 'Not provided'}</p>
              </div>
              
              <p>What happens next:</p>
              <ol>
                <li>Our design consultant will review your requirements</li>
                <li>We'll contact you within 24 hours to schedule a consultation</li>
                <li>During the consultation, we'll discuss your vision and provide initial recommendations</li>
                <li>We'll create a customized design proposal for your space</li>
              </ol>
              
              <p>In the meantime, feel free to browse our portfolio and product catalog for inspiration.</p>
              
              <p>Best regards,<br>The ${this.companyName} Team</p>
            </div>
            <div class="footer">
              <p>© 2024 ${this.companyName}. All rights reserved.</p>
              <p>Questions? Contact <NAME_EMAIL></p>
            </div>
          </div>
        </body>
        </html>
      `;

      const mailOptions = {
        from: this.fromEmail,
        to: customerEmail,
        subject,
        html
      };

      await transporter.sendMail(mailOptions);
      logger.info(`Consultation request email sent to ${customerEmail}`);

    } catch (error) {
      logger.error('Failed to send consultation request email:', error);
      throw error;
    }
  }

  // Send project status update email
  async sendProjectStatusUpdateEmail(customerEmail, customerName, project, newStatus) {
    try {
      const subject = `Project Update: ${project.project_name}`;
      
      const statusMessages = {
        'consultation_scheduled': 'Your consultation has been scheduled',
        'in_progress': 'Your project is now in progress',
        'design_ready': 'Your design is ready for review',
        'approved': 'Your design has been approved',
        'execution': 'Project execution has begun',
        'completed': 'Your project has been completed'
      };

      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Project Update - ${this.companyName}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #17a2b8; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .status-update { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #17a2b8; }
            .button { display: inline-block; background: #17a2b8; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Project Update</h1>
            </div>
            <div class="content">
              <h2>Hello ${customerName},</h2>
              <p>We have an update on your interior design project!</p>
              
              <div class="status-update">
                <h3>${project.project_name}</h3>
                <p><strong>Status Update:</strong> ${statusMessages[newStatus] || newStatus}</p>
                <p><strong>Project ID:</strong> ${project.id}</p>
              </div>
              
              <p>You can track your project progress and view all updates in your customer portal.</p>
              
              <div style="text-align: center;">
                <a href="${this.frontendUrl}/profile/projects/${project.id}" class="button">View Project Details</a>
              </div>
              
              <p>If you have any questions about your project, please don't hesitate to contact your assigned designer or reach out to our support team.</p>
              
              <p>Best regards,<br>The ${this.companyName} Team</p>
            </div>
            <div class="footer">
              <p>© 2024 ${this.companyName}. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      const mailOptions = {
        from: this.fromEmail,
        to: customerEmail,
        subject,
        html
      };

      await transporter.sendMail(mailOptions);
      logger.info(`Project status update email sent to ${customerEmail}`);

    } catch (error) {
      logger.error('Failed to send project status update email:', error);
      throw error;
    }
  }

  // Send order confirmation email
  async sendOrderConfirmationEmail(customerEmail, customerName, orderDetails) {
    try {
      const subject = `Order Confirmation - ${orderDetails.orderNumber}`;
      
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Order Confirmation - ${this.companyName}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .order-details { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .item { border-bottom: 1px solid #eee; padding: 10px 0; }
            .total { font-weight: bold; font-size: 18px; color: #28a745; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Order Confirmed!</h1>
            </div>
            <div class="content">
              <h2>Hello ${customerName},</h2>
              <p>Thank you for your order! We've received your order and it's being processed.</p>
              
              <div class="order-details">
                <h3>Order Details</h3>
                <p><strong>Order Number:</strong> ${orderDetails.orderNumber}</p>
                <p><strong>Order Date:</strong> ${new Date().toLocaleDateString()}</p>
                <p><strong>Total Amount:</strong> <span class="total">₹${orderDetails.total}</span></p>
              </div>
              
              <p>We'll send you another email with tracking information once your order ships.</p>
              
              <p>Best regards,<br>The ${this.companyName} Team</p>
            </div>
            <div class="footer">
              <p>© 2024 ${this.companyName}. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      const mailOptions = {
        from: this.fromEmail,
        to: customerEmail,
        subject,
        html
      };

      await transporter.sendMail(mailOptions);
      logger.info(`Order confirmation email sent to ${customerEmail}`);

    } catch (error) {
      logger.error('Failed to send order confirmation email:', error);
      throw error;
    }
  }

  // Send generic notification email
  async sendNotificationEmail(email, subject, message, type = 'info') {
    try {
      const colors = {
        info: '#17a2b8',
        success: '#28a745',
        warning: '#ffc107',
        error: '#dc3545'
      };

      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: ${colors[type]}; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
            .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>${subject}</h1>
            </div>
            <div class="content">
              ${message}
              <p>Best regards,<br>The ${this.companyName} Team</p>
            </div>
            <div class="footer">
              <p>© 2024 ${this.companyName}. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `;

      const mailOptions = {
        from: this.fromEmail,
        to: email,
        subject,
        html
      };

      await transporter.sendMail(mailOptions);
      logger.info(`Notification email sent to ${email}: ${subject}`);

    } catch (error) {
      logger.error('Failed to send notification email:', error);
      throw error;
    }
  }
}

module.exports = new EmailService();
