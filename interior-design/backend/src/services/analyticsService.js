const { Op, Sequelize } = require('sequelize');
const { sequelize } = require('../database/connection');
const User = require('../models/User');
const Product = require('../models/Product');
const Project = require('../models/Project');
const Category = require('../models/Category');
const { WhatsAppConversation } = require('../models/WhatsApp');
const logger = require('../utils/logger');

class AnalyticsService {
  // Get dashboard overview statistics
  async getDashboardStats(dateRange = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      const [
        totalCustomers,
        newCustomers,
        totalProducts,
        activeProducts,
        totalProjects,
        activeProjects,
        completedProjects,
        totalConversations,
        activeConversations
      ] = await Promise.all([
        // Total customers
        User.count({
          where: { user_type: 'customer', is_active: true }
        }),
        
        // New customers in date range
        User.count({
          where: {
            user_type: 'customer',
            is_active: true,
            created_at: { [Op.gte]: startDate }
          }
        }),
        
        // Total products
        Product.count(),
        
        // Active products
        Product.count({
          where: { status: 'active' }
        }),
        
        // Total projects
        Project.count(),
        
        // Active projects
        Project.count({
          where: {
            status: {
              [Op.in]: ['inquiry', 'consultation_scheduled', 'in_progress', 'design_ready', 'approved', 'execution']
            }
          }
        }),
        
        // Completed projects
        Project.count({
          where: { status: 'completed' }
        }),
        
        // Total WhatsApp conversations
        WhatsAppConversation.count(),
        
        // Active WhatsApp conversations
        WhatsAppConversation.count({
          where: {
            status: { [Op.in]: ['open', 'in_progress'] }
          }
        })
      ]);

      return {
        customers: {
          total: totalCustomers,
          new: newCustomers,
          growth: totalCustomers > 0 ? ((newCustomers / totalCustomers) * 100).toFixed(1) : 0
        },
        products: {
          total: totalProducts,
          active: activeProducts,
          activePercentage: totalProducts > 0 ? ((activeProducts / totalProducts) * 100).toFixed(1) : 0
        },
        projects: {
          total: totalProjects,
          active: activeProjects,
          completed: completedProjects,
          completionRate: totalProjects > 0 ? ((completedProjects / totalProjects) * 100).toFixed(1) : 0
        },
        conversations: {
          total: totalConversations,
          active: activeConversations,
          responseRate: totalConversations > 0 ? ((activeConversations / totalConversations) * 100).toFixed(1) : 0
        }
      };

    } catch (error) {
      logger.error('Error getting dashboard stats:', error);
      throw error;
    }
  }

  // Get customer analytics
  async getCustomerAnalytics(dateRange = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      // Customer registration trends
      const registrationTrends = await User.findAll({
        attributes: [
          [Sequelize.fn('DATE', Sequelize.col('created_at')), 'date'],
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        where: {
          user_type: 'customer',
          created_at: { [Op.gte]: startDate }
        },
        group: [Sequelize.fn('DATE', Sequelize.col('created_at'))],
        order: [[Sequelize.fn('DATE', Sequelize.col('created_at')), 'ASC']],
        raw: true
      });

      // Customer by location
      const customersByLocation = await User.findAll({
        attributes: [
          'city',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        where: {
          user_type: 'customer',
          is_active: true,
          city: { [Op.ne]: null }
        },
        group: ['city'],
        order: [[Sequelize.fn('COUNT', Sequelize.col('id')), 'DESC']],
        limit: 10,
        raw: true
      });

      // Customer engagement metrics
      const engagementMetrics = await sequelize.query(`
        SELECT 
          COUNT(DISTINCT u.id) as total_customers,
          COUNT(DISTINCT p.customer_id) as customers_with_projects,
          COUNT(DISTINCT w.customer_id) as customers_with_conversations,
          AVG(CASE WHEN p.customer_id IS NOT NULL THEN 1 ELSE 0 END) as project_engagement_rate,
          AVG(CASE WHEN w.customer_id IS NOT NULL THEN 1 ELSE 0 END) as conversation_engagement_rate
        FROM users u
        LEFT JOIN projects p ON u.id = p.customer_id
        LEFT JOIN whatsapp_conversations w ON u.id = w.customer_id
        WHERE u.user_type = 'customer' AND u.is_active = true
      `, { type: Sequelize.QueryTypes.SELECT });

      return {
        registrationTrends,
        customersByLocation,
        engagementMetrics: engagementMetrics[0]
      };

    } catch (error) {
      logger.error('Error getting customer analytics:', error);
      throw error;
    }
  }

  // Get product analytics
  async getProductAnalytics(dateRange = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      // Most viewed products
      const mostViewedProducts = await Product.findAll({
        attributes: ['id', 'name', 'view_count', 'price'],
        include: [{
          model: Category,
          as: 'category',
          attributes: ['name']
        }],
        where: { status: 'active' },
        order: [['view_count', 'DESC']],
        limit: 10
      });

      // Products by category
      const productsByCategory = await Category.findAll({
        attributes: [
          'id',
          'name',
          [Sequelize.fn('COUNT', Sequelize.col('products.id')), 'product_count']
        ],
        include: [{
          model: Product,
          as: 'products',
          attributes: [],
          where: { status: 'active' },
          required: false
        }],
        group: ['Category.id', 'Category.name'],
        order: [[Sequelize.fn('COUNT', Sequelize.col('products.id')), 'DESC']],
        raw: true
      });

      // Price range distribution
      const priceRanges = await sequelize.query(`
        SELECT 
          CASE 
            WHEN price < 10000 THEN 'Under ₹10K'
            WHEN price BETWEEN 10000 AND 50000 THEN '₹10K - ₹50K'
            WHEN price BETWEEN 50000 AND 100000 THEN '₹50K - ₹1L'
            WHEN price BETWEEN 100000 AND 500000 THEN '₹1L - ₹5L'
            ELSE 'Above ₹5L'
          END as price_range,
          COUNT(*) as count
        FROM products 
        WHERE status = 'active'
        GROUP BY price_range
        ORDER BY MIN(price)
      `, { type: Sequelize.QueryTypes.SELECT });

      // Product performance metrics
      const performanceMetrics = await Product.findAll({
        attributes: [
          [Sequelize.fn('AVG', Sequelize.col('view_count')), 'avg_views'],
          [Sequelize.fn('MAX', Sequelize.col('view_count')), 'max_views'],
          [Sequelize.fn('MIN', Sequelize.col('view_count')), 'min_views'],
          [Sequelize.fn('AVG', Sequelize.col('price')), 'avg_price'],
          [Sequelize.fn('MAX', Sequelize.col('price')), 'max_price'],
          [Sequelize.fn('MIN', Sequelize.col('price')), 'min_price']
        ],
        where: { status: 'active' },
        raw: true
      });

      return {
        mostViewedProducts,
        productsByCategory,
        priceRanges,
        performanceMetrics: performanceMetrics[0]
      };

    } catch (error) {
      logger.error('Error getting product analytics:', error);
      throw error;
    }
  }

  // Get project analytics
  async getProjectAnalytics(dateRange = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      // Project status distribution
      const projectsByStatus = await Project.findAll({
        attributes: [
          'status',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        group: ['status'],
        order: [[Sequelize.fn('COUNT', Sequelize.col('id')), 'DESC']],
        raw: true
      });

      // Project trends over time
      const projectTrends = await Project.findAll({
        attributes: [
          [Sequelize.fn('DATE', Sequelize.col('created_at')), 'date'],
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        where: {
          created_at: { [Op.gte]: startDate }
        },
        group: [Sequelize.fn('DATE', Sequelize.col('created_at'))],
        order: [[Sequelize.fn('DATE', Sequelize.col('created_at')), 'ASC']],
        raw: true
      });

      // Project types distribution
      const projectsByType = await Project.findAll({
        attributes: [
          'project_type',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        group: ['project_type'],
        order: [[Sequelize.fn('COUNT', Sequelize.col('id')), 'DESC']],
        raw: true
      });

      // Average project completion time
      const completionTimeStats = await sequelize.query(`
        SELECT 
          AVG(EXTRACT(DAY FROM (completion_date - created_at))) as avg_completion_days,
          MIN(EXTRACT(DAY FROM (completion_date - created_at))) as min_completion_days,
          MAX(EXTRACT(DAY FROM (completion_date - created_at))) as max_completion_days
        FROM projects 
        WHERE status = 'completed' AND completion_date IS NOT NULL
      `, { type: Sequelize.QueryTypes.SELECT });

      // Budget analysis
      const budgetAnalysis = await sequelize.query(`
        SELECT 
          CASE 
            WHEN budget_max < 100000 THEN 'Under ₹1L'
            WHEN budget_max BETWEEN 100000 AND 500000 THEN '₹1L - ₹5L'
            WHEN budget_max BETWEEN 500000 AND 1000000 THEN '₹5L - ₹10L'
            WHEN budget_max BETWEEN 1000000 AND 2000000 THEN '₹10L - ₹20L'
            ELSE 'Above ₹20L'
          END as budget_range,
          COUNT(*) as count,
          AVG(EXTRACT(DAY FROM (completion_date - created_at))) as avg_completion_days
        FROM projects 
        WHERE budget_max IS NOT NULL
        GROUP BY budget_range
        ORDER BY MIN(budget_max)
      `, { type: Sequelize.QueryTypes.SELECT });

      return {
        projectsByStatus,
        projectTrends,
        projectsByType,
        completionTimeStats: completionTimeStats[0],
        budgetAnalysis
      };

    } catch (error) {
      logger.error('Error getting project analytics:', error);
      throw error;
    }
  }

  // Get WhatsApp analytics
  async getWhatsAppAnalytics(dateRange = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      // Conversation trends
      const conversationTrends = await WhatsAppConversation.findAll({
        attributes: [
          [Sequelize.fn('DATE', Sequelize.col('created_at')), 'date'],
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        where: {
          created_at: { [Op.gte]: startDate }
        },
        group: [Sequelize.fn('DATE', Sequelize.col('created_at'))],
        order: [[Sequelize.fn('DATE', Sequelize.col('created_at')), 'ASC']],
        raw: true
      });

      // Conversation status distribution
      const conversationsByStatus = await WhatsAppConversation.findAll({
        attributes: [
          'status',
          [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
        ],
        group: ['status'],
        order: [[Sequelize.fn('COUNT', Sequelize.col('id')), 'DESC']],
        raw: true
      });

      // Response time analysis
      const responseTimeStats = await sequelize.query(`
        SELECT 
          AVG(EXTRACT(EPOCH FROM (first_response_at - created_at))/60) as avg_response_time_minutes,
          MIN(EXTRACT(EPOCH FROM (first_response_at - created_at))/60) as min_response_time_minutes,
          MAX(EXTRACT(EPOCH FROM (first_response_at - created_at))/60) as max_response_time_minutes
        FROM whatsapp_conversations 
        WHERE first_response_at IS NOT NULL
      `, { type: Sequelize.QueryTypes.SELECT });

      // Conversion rate (conversations that led to projects)
      const conversionStats = await sequelize.query(`
        SELECT 
          COUNT(DISTINCT w.id) as total_conversations,
          COUNT(DISTINCT p.id) as conversations_with_projects,
          (COUNT(DISTINCT p.id)::float / COUNT(DISTINCT w.id) * 100) as conversion_rate
        FROM whatsapp_conversations w
        LEFT JOIN projects p ON w.project_id = p.id
        WHERE w.created_at >= :startDate
      `, {
        replacements: { startDate },
        type: Sequelize.QueryTypes.SELECT
      });

      return {
        conversationTrends,
        conversationsByStatus,
        responseTimeStats: responseTimeStats[0],
        conversionStats: conversionStats[0]
      };

    } catch (error) {
      logger.error('Error getting WhatsApp analytics:', error);
      throw error;
    }
  }

  // Get revenue analytics (placeholder for future order system)
  async getRevenueAnalytics(dateRange = 30) {
    try {
      // This is a placeholder for when order/payment system is implemented
      // For now, we'll analyze project budgets as potential revenue
      
      const revenueProjection = await sequelize.query(`
        SELECT 
          DATE_TRUNC('month', created_at) as month,
          COUNT(*) as project_count,
          AVG(budget_max) as avg_budget,
          SUM(budget_max) as total_potential_revenue
        FROM projects 
        WHERE budget_max IS NOT NULL 
          AND status IN ('approved', 'execution', 'completed')
          AND created_at >= NOW() - INTERVAL '12 months'
        GROUP BY DATE_TRUNC('month', created_at)
        ORDER BY month
      `, { type: Sequelize.QueryTypes.SELECT });

      const budgetByStatus = await sequelize.query(`
        SELECT 
          status,
          COUNT(*) as project_count,
          AVG(budget_max) as avg_budget,
          SUM(budget_max) as total_budget
        FROM projects 
        WHERE budget_max IS NOT NULL
        GROUP BY status
        ORDER BY total_budget DESC
      `, { type: Sequelize.QueryTypes.SELECT });

      return {
        revenueProjection,
        budgetByStatus,
        note: 'Revenue analytics based on project budgets. Actual revenue tracking will be available when order system is implemented.'
      };

    } catch (error) {
      logger.error('Error getting revenue analytics:', error);
      throw error;
    }
  }

  // Get comprehensive analytics report
  async getComprehensiveReport(dateRange = 30) {
    try {
      const [
        dashboardStats,
        customerAnalytics,
        productAnalytics,
        projectAnalytics,
        whatsappAnalytics,
        revenueAnalytics
      ] = await Promise.all([
        this.getDashboardStats(dateRange),
        this.getCustomerAnalytics(dateRange),
        this.getProductAnalytics(dateRange),
        this.getProjectAnalytics(dateRange),
        this.getWhatsAppAnalytics(dateRange),
        this.getRevenueAnalytics(dateRange)
      ]);

      return {
        dateRange,
        generatedAt: new Date(),
        dashboardStats,
        customerAnalytics,
        productAnalytics,
        projectAnalytics,
        whatsappAnalytics,
        revenueAnalytics
      };

    } catch (error) {
      logger.error('Error generating comprehensive report:', error);
      throw error;
    }
  }

  // Track custom events (for future use)
  async trackEvent(eventName, eventData, userId = null) {
    try {
      // This is a placeholder for custom event tracking
      // Could be implemented with a separate events table or external analytics service
      
      logger.info('Event tracked:', {
        eventName,
        eventData,
        userId,
        timestamp: new Date()
      });

      // TODO: Implement actual event storage/tracking
      return { success: true, eventName, timestamp: new Date() };

    } catch (error) {
      logger.error('Error tracking event:', error);
      throw error;
    }
  }
}

module.exports = new AnalyticsService();
