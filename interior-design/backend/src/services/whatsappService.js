const axios = require('axios');
const { s3Service } = require('./s3Service');
const logger = require('../utils/logger');

class WhatsAppService {
  constructor() {
    this.accessToken = process.env.WHATSAPP_TOKEN;
    this.phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;
    this.webhookVerifyToken = process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN;
    this.apiVersion = 'v18.0';
    this.baseURL = `https://graph.facebook.com/${this.apiVersion}`;
  }

  // Send text message
  async sendTextMessage(phoneNumber, message, replyToMessageId = null) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: phoneNumber,
        type: 'text',
        text: {
          body: message
        }
      };

      if (replyToMessageId) {
        payload.context = {
          message_id: replyToMessageId
        };
      }

      const response = await this.makeRequest('POST', `/messages`, payload);
      
      logger.info('WhatsApp text message sent', {
        to: phoneNumber,
        messageId: response.data.messages[0]?.id
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to send WhatsApp text message:', error);
      throw new Error(`WhatsApp API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // Send template message
  async sendTemplateMessage(phoneNumber, templateName, templateParams = []) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: phoneNumber,
        type: 'template',
        template: {
          name: templateName,
          language: {
            code: 'en'
          }
        }
      };

      if (templateParams.length > 0) {
        payload.template.components = [
          {
            type: 'body',
            parameters: templateParams.map(param => ({
              type: 'text',
              text: param
            }))
          }
        ];
      }

      const response = await this.makeRequest('POST', `/messages`, payload);
      
      logger.info('WhatsApp template message sent', {
        to: phoneNumber,
        template: templateName,
        messageId: response.data.messages[0]?.id
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to send WhatsApp template message:', error);
      throw new Error(`WhatsApp API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // Send image message
  async sendImageMessage(phoneNumber, imageUrl, caption = null) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: phoneNumber,
        type: 'image',
        image: {
          link: imageUrl
        }
      };

      if (caption) {
        payload.image.caption = caption;
      }

      const response = await this.makeRequest('POST', `/messages`, payload);
      
      logger.info('WhatsApp image message sent', {
        to: phoneNumber,
        imageUrl,
        messageId: response.data.messages[0]?.id
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to send WhatsApp image message:', error);
      throw new Error(`WhatsApp API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // Send document message
  async sendDocumentMessage(phoneNumber, documentUrl, filename, caption = null) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        to: phoneNumber,
        type: 'document',
        document: {
          link: documentUrl,
          filename: filename
        }
      };

      if (caption) {
        payload.document.caption = caption;
      }

      const response = await this.makeRequest('POST', `/messages`, payload);
      
      logger.info('WhatsApp document message sent', {
        to: phoneNumber,
        documentUrl,
        filename,
        messageId: response.data.messages[0]?.id
      });

      return response.data;
    } catch (error) {
      logger.error('Failed to send WhatsApp document message:', error);
      throw new Error(`WhatsApp API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // Download media from WhatsApp
  async downloadMedia(mediaId) {
    try {
      // First, get the media URL
      const mediaResponse = await this.makeRequest('GET', `/${mediaId}`);
      const mediaUrl = mediaResponse.data.url;
      
      // Download the media file
      const fileResponse = await axios.get(mediaUrl, {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        },
        responseType: 'stream'
      });

      // Convert stream to buffer
      const chunks = [];
      for await (const chunk of fileResponse.data) {
        chunks.push(chunk);
      }
      const buffer = Buffer.concat(chunks);

      logger.info('WhatsApp media downloaded', {
        mediaId,
        size: buffer.length,
        mimeType: mediaResponse.data.mime_type
      });

      return {
        buffer,
        mimeType: mediaResponse.data.mime_type,
        filename: `whatsapp_media_${mediaId}`
      };
    } catch (error) {
      logger.error('Failed to download WhatsApp media:', error);
      throw new Error(`WhatsApp Media Download Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // Upload media to S3 and get public URL
  async processAndUploadMedia(mediaId, folder = 'whatsapp/media') {
    try {
      const { buffer, mimeType, filename } = await this.downloadMedia(mediaId);
      
      // Upload to S3
      const uploadResult = await s3Service.uploadFile(
        buffer,
        filename,
        mimeType,
        folder
      );

      logger.info('WhatsApp media uploaded to S3', {
        mediaId,
        s3Key: uploadResult.key,
        s3Url: uploadResult.url
      });

      return uploadResult;
    } catch (error) {
      logger.error('Failed to process and upload WhatsApp media:', error);
      throw error;
    }
  }

  // Mark message as read
  async markMessageAsRead(messageId) {
    try {
      const payload = {
        messaging_product: 'whatsapp',
        status: 'read',
        message_id: messageId
      };

      const response = await this.makeRequest('POST', `/messages`, payload);
      
      logger.info('WhatsApp message marked as read', { messageId });
      return response.data;
    } catch (error) {
      logger.error('Failed to mark WhatsApp message as read:', error);
      throw new Error(`WhatsApp API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // Get business profile
  async getBusinessProfile() {
    try {
      const response = await this.makeRequest('GET', `/${this.phoneNumberId}`, null, {
        fields: 'id,verified_name,display_phone_number,quality_rating'
      });

      logger.info('WhatsApp business profile retrieved');
      return response.data;
    } catch (error) {
      logger.error('Failed to get WhatsApp business profile:', error);
      throw new Error(`WhatsApp API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  // Verify webhook
  verifyWebhook(mode, token, challenge) {
    if (mode === 'subscribe' && token === this.webhookVerifyToken) {
      logger.info('WhatsApp webhook verified successfully');
      return challenge;
    } else {
      logger.warn('WhatsApp webhook verification failed', { mode, token });
      return null;
    }
  }

  // Process incoming webhook
  processWebhook(body) {
    try {
      const changes = body.entry?.[0]?.changes?.[0];
      
      if (!changes || changes.field !== 'messages') {
        logger.warn('Invalid WhatsApp webhook payload');
        return null;
      }

      const value = changes.value;
      
      // Process incoming messages
      if (value.messages) {
        return value.messages.map(message => ({
          type: 'message',
          messageId: message.id,
          from: message.from,
          timestamp: new Date(parseInt(message.timestamp) * 1000),
          messageType: message.type,
          content: this.extractMessageContent(message),
          context: message.context || null
        }));
      }

      // Process message status updates
      if (value.statuses) {
        return value.statuses.map(status => ({
          type: 'status',
          messageId: status.id,
          recipient: status.recipient_id,
          status: status.status,
          timestamp: new Date(parseInt(status.timestamp) * 1000),
          errors: status.errors || null
        }));
      }

      return null;
    } catch (error) {
      logger.error('Failed to process WhatsApp webhook:', error);
      throw error;
    }
  }

  // Extract message content based on type
  extractMessageContent(message) {
    switch (message.type) {
      case 'text':
        return {
          text: message.text.body
        };
      
      case 'image':
        return {
          mediaId: message.image.id,
          caption: message.image.caption || null,
          mimeType: message.image.mime_type
        };
      
      case 'document':
        return {
          mediaId: message.document.id,
          filename: message.document.filename,
          caption: message.document.caption || null,
          mimeType: message.document.mime_type
        };
      
      case 'audio':
        return {
          mediaId: message.audio.id,
          mimeType: message.audio.mime_type
        };
      
      case 'video':
        return {
          mediaId: message.video.id,
          caption: message.video.caption || null,
          mimeType: message.video.mime_type
        };
      
      case 'location':
        return {
          latitude: message.location.latitude,
          longitude: message.location.longitude,
          name: message.location.name || null,
          address: message.location.address || null
        };
      
      case 'contacts':
        return {
          contacts: message.contacts
        };
      
      default:
        return {
          unsupported: true,
          type: message.type
        };
    }
  }

  // Make HTTP request to WhatsApp API
  async makeRequest(method, endpoint, data = null, params = null) {
    try {
      const config = {
        method,
        url: `${this.baseURL}/${this.phoneNumberId}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      };

      if (data) {
        config.data = data;
      }

      if (params) {
        config.params = params;
      }

      const response = await axios(config);
      return response;
    } catch (error) {
      logger.error('WhatsApp API request failed:', {
        method,
        endpoint,
        status: error.response?.status,
        error: error.response?.data
      });
      throw error;
    }
  }

  // Health check
  async healthCheck() {
    try {
      await this.getBusinessProfile();
      return {
        status: 'healthy',
        timestamp: new Date()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  // Common template messages
  templates = {
    // Welcome message for new customers
    welcome: (customerName) => 
      `Hello ${customerName}! 👋 Welcome to InteriorCraft. We're excited to help you transform your space. How can we assist you today?`,
    
    // Consultation booking confirmation
    consultationBooked: (customerName, date, time) => 
      `Hi ${customerName}! Your consultation has been booked for ${date} at ${time}. Our designer will contact you soon. Thank you for choosing InteriorCraft! 🏡`,
    
    // Project status update
    projectUpdate: (customerName, projectName, status) => 
      `Hello ${customerName}! Update on your project "${projectName}": Status has been updated to ${status}. We'll keep you posted on further progress. 📋`,
    
    // Quote ready notification
    quoteReady: (customerName, projectName) => 
      `Hi ${customerName}! Your quote for "${projectName}" is ready. Please check your email or contact us to review the details. 💰`,
    
    // Support response
    supportResponse: (customerName) => 
      `Hello ${customerName}! Thank you for reaching out. Our support team will get back to you within 24 hours. For urgent matters, please call us directly. 🔧`
  };
}

// Create singleton instance
const whatsappService = new WhatsAppService();

module.exports = {
  whatsappService,
  WhatsAppService
};