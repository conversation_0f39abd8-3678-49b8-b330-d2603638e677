// src/routes/projects.js
const express = require("express");
const { body, param, query, validationResult } = require("express-validator");
const rateLimit = require("express-rate-limit");

const { checkJwt, syncUser, requireStaff, requireCustomer } = require("../middleware/auth");
const { Project, User } = require("../models");
const { asyncHandler, handleValidationError, NotFoundError } = require("../middleware/errorHandler");
const logger = require("../utils/logger");

const router = express.Router();

// Rate limiting
const projectLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { error: "Too many requests, please try again later" },
});

// Validation middleware
const validateProjectId = [param("id").isUUID().withMessage("Project ID must be a valid UUID")];

const validateProject = [
  body("title").trim().isLength({ min: 1, max: 255 }).withMessage("Title must be between 1 and 255 characters"),
  body("description").optional().trim().isLength({ max: 5000 }).withMessage("Description must be less than 5000 characters"),
  body("customer_id").isUUID().withMessage("Customer ID must be a valid UUID"),
  body("budget").optional().isFloat({ min: 0 }).withMessage("Budget must be a positive number"),
  body("status").optional().isIn(["inquiry", "consultation", "design", "approval", "execution", "completed", "cancelled"]).withMessage("Invalid status"),
  body("priority").optional().isIn(["low", "medium", "high", "urgent"]).withMessage("Invalid priority"),
  body("start_date").optional().isISO8601().withMessage("Start date must be a valid date"),
  body("end_date").optional().isISO8601().withMessage("End date must be a valid date"),
];

// GET /api/v1/projects - Get all projects (Staff only)
router.get(
  "/",
  projectLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  query("limit").optional().isInt({ min: 1, max: 100 }).withMessage("Limit must be between 1 and 100"),
  query("status").optional().isIn(["inquiry", "consultation", "design", "approval", "execution", "completed", "cancelled"]).withMessage("Invalid status"),
  query("priority").optional().isIn(["low", "medium", "high", "urgent"]).withMessage("Invalid priority"),
  query("search").optional().trim().isLength({ min: 1 }).withMessage("Search query cannot be empty"),
  query("sort").optional().isIn(["title", "status", "priority", "budget", "created_at", "start_date", "end_date"]).withMessage("Invalid sort field"),
  query("order").optional().isIn(["ASC", "DESC", "asc", "desc"]).withMessage("Order must be ASC, DESC, asc, or desc"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 20, status, priority, search, sort = "created_at", order = "DESC" } = req.query;
    const normalizedOrder = order.toUpperCase(); // Normalize to uppercase

    try {
      const { Op } = require("sequelize");
      const whereClause = {};

      // Apply filters
      if (status) {
        whereClause.status = status;
      }

      if (priority) {
        whereClause.priority = priority;
      }

      if (search) {
        whereClause[Op.or] = [{ title: { [Op.iLike]: `%${search}%` } }, { description: { [Op.iLike]: `%${search}%` } }, { project_id: { [Op.iLike]: `%${search}%` } }];
      }

      const { rows: projects, count } = await Project.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: "customer",
            attributes: ["id", "first_name", "last_name", "email", "phone"],
          },
          {
            model: User,
            as: "designer",
            attributes: ["id", "first_name", "last_name", "email"],
            required: false,
          },
          {
            model: User,
            as: "salesPerson",
            attributes: ["id", "first_name", "last_name", "email"],
            required: false,
          },
        ],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        order: [[sort, normalizedOrder]],
      });

      res.json({
        success: true,
        projects,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit),
          hasNext: parseInt(page) < Math.ceil(count / limit),
          hasPrev: parseInt(page) > 1,
        },
      });
    } catch (error) {
      logger.error("Get projects error:", error);
      throw error;
    }
  })
);

module.exports = router;
