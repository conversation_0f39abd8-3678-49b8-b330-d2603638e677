// src/routes/projects.js
const express = require('express');
const { query } = require('../database/connection');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get all projects (protected)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, status, sortBy = 'created_at', sortOrder = 'desc' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = '';
    let params = [];

    if (status) {
      whereClause = 'WHERE p.status = $1';
      params.push(status);
    }

    const projectsQuery = `
      SELECT 
        p.*,
        c.first_name || ' ' || c.last_name as customer_name,
        c.phone as customer_phone,
        designer.first_name || ' ' || designer.last_name as designer_name,
        sales.first_name || ' ' || sales.last_name as sales_name
      FROM projects p
      LEFT JOIN customers c ON p.customer_id = c.id
      LEFT JOIN users designer ON p.assigned_designer_id = designer.id
      LEFT JOIN users sales ON p.assigned_sales_id = sales.id
      ${whereClause}
      ORDER BY p.${sortBy} ${sortOrder}
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;
    params.push(limit, offset);

    const result = await query(projectsQuery, params);

    res.json({
      success: true,
      data: { projects: result.rows }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch projects'
    });
  }
});

module.exports = router;