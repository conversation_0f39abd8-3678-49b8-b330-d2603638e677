// src/routes/projects.js
const express = require("express");
const { body, param, query, validationResult } = require("express-validator");
const rateLimit = require("express-rate-limit");

const { checkJwt, syncUser, requireStaff, requireCustomer } = require("../middleware/auth");
const { asyncHandler, handleValidationError, NotFoundError } = require("../middleware/errorHandler");
const logger = require("../utils/logger");

const router = express.Router();

// Rate limiting
const projectLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { error: "Too many requests, please try again later" },
});

// Get all projects (Staff only)
router.get(
  "/",
  projectLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 20, status, sortBy = "created_at", sortOrder = "desc" } = req.query;
    const offset = (page - 1) * limit;
    const { query: dbQuery } = require("../database/connection");

    let whereClause = "";
    let params = [];

    if (status) {
      whereClause = "WHERE p.status = $1";
      params.push(status);
    }

    const projectsQuery = `
      SELECT
        p.*,
        u.first_name || ' ' || u.last_name as customer_name,
        u.phone as customer_phone,
        designer.first_name || ' ' || designer.last_name as designer_name,
        sales.first_name || ' ' || sales.last_name as sales_name
      FROM projects p
      LEFT JOIN users u ON p.customer_id = u.id
      LEFT JOIN users designer ON p.designer_id = designer.id
      LEFT JOIN users sales ON p.sales_person_id = sales.id
      ${whereClause}
      ORDER BY p.${sortBy} ${sortOrder}
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;
    params.push(limit, offset);

    const result = await dbQuery(projectsQuery, params);

    res.json({
      success: true,
      data: { projects: result.rows },
    });
  })
);

module.exports = router;
