// src/routes/customers.js
const express = require("express");
const { body, param, query: queryValidator, validationResult } = require("express-validator");
const rateLimit = require("express-rate-limit");

const { checkJwt, syncUser, requireStaff, requireCustomer, requireOwnership } = require("../middleware/auth");
const { User, Project } = require("../models");
const { asyncHandler, handleValidationError, NotFoundError } = require("../middleware/errorHandler");
const logger = require("../utils/logger");

const router = express.Router();

// Rate limiting
const customerLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { error: "Too many requests, please try again later" },
});

// Validation middleware
const validateCustomerId = [param("id").isUUID().withMessage("Customer ID must be a valid UUID")];

const validateCustomerUpdate = [
  body("first_name").optional().trim().isLength({ min: 1, max: 100 }).withMessage("First name must be between 1 and 100 characters"),
  body("last_name").optional().trim().isLength({ max: 100 }).withMessage("Last name must be less than 100 characters"),
  body("phone")
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage("Invalid phone number format"),
  body("timezone").optional().isIn(["Asia/Kolkata", "UTC", "America/New_York", "Europe/London"]).withMessage("Invalid timezone"),
  body("language").optional().isIn(["en", "hi", "es", "fr"]).withMessage("Invalid language code"),
];

// GET /api/v1/customers - Get all customers (Staff only)
router.get(
  "/",
  customerLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  queryValidator("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  queryValidator("limit").optional().isInt({ min: 1, max: 100 }).withMessage("Limit must be between 1 and 100"),
  queryValidator("search").optional().trim().isLength({ min: 1 }).withMessage("Search query cannot be empty"),
  queryValidator("is_active").optional().isBoolean().withMessage("is_active must be boolean"),
  queryValidator("sort").optional().isIn(["first_name", "last_name", "email", "created_at", "last_login_at"]).withMessage("Invalid sort field"),
  queryValidator("order").optional().isIn(["ASC", "DESC"]).withMessage("Order must be ASC or DESC"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 20, search, is_active, sort = "created_at", order = "DESC" } = req.query;

    try {
      const { Op } = require("sequelize");
      const whereClause = { user_type: "customer" };

      // Apply filters
      if (is_active !== undefined) {
        whereClause.is_active = is_active === "true";
      }

      if (search) {
        whereClause[Op.or] = [
          { first_name: { [Op.iLike]: `%${search}%` } },
          { last_name: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { phone: { [Op.iLike]: `%${search}%` } },
        ];
      }

      const { rows: customers, count } = await User.findAndCountAll({
        where: whereClause,
        attributes: { exclude: ["auth0_id"] },
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        order: [[sort, order]],
      });

      res.json({
        success: true,
        customers: customers.map((customer) => customer.toSafeJSON()),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit),
          hasNext: parseInt(page) < Math.ceil(count / limit),
          hasPrev: parseInt(page) > 1,
        },
      });
    } catch (error) {
      logger.error("Get customers error:", error);
      throw error;
    }
  })
);

// GET /api/v1/customers/:id - Get customer by ID (Staff only)
router.get(
  "/:id",
  customerLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateCustomerId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    try {
      const customer = await User.findOne({
        where: { id, user_type: "customer" },
        attributes: { exclude: ["auth0_id"] },
        include: [
          {
            model: Project,
            as: "projects",
            limit: 5,
            order: [["created_at", "DESC"]],
          },
        ],
      });

      if (!customer) {
        throw new NotFoundError("Customer");
      }

      res.json({
        success: true,
        customer: customer.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Get customer error:", error);
      throw error;
    }
  })
);

// PUT /api/v1/customers/:id - Update customer (Staff only)
router.put(
  "/:id",
  customerLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateCustomerId,
  validateCustomerUpdate,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;

    try {
      const customer = await User.findOne({
        where: { id, user_type: "customer" },
      });

      if (!customer) {
        throw new NotFoundError("Customer");
      }

      const updatedCustomer = await customer.update(updateData);

      logger.info(`Customer updated: ${customer.email} by ${req.user.email}`);

      res.json({
        success: true,
        message: "Customer updated successfully",
        customer: updatedCustomer.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Update customer error:", error);
      throw error;
    }
  })
);

// PATCH /api/v1/customers/:id/status - Toggle customer status (Staff only)
router.patch(
  "/:id/status",
  customerLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateCustomerId,
  body("is_active").isBoolean().withMessage("is_active must be a boolean"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { is_active } = req.body;

    try {
      const customer = await User.findOne({
        where: { id, user_type: "customer" },
      });

      if (!customer) {
        throw new NotFoundError("Customer");
      }

      const updatedCustomer = await customer.update({ is_active });

      logger.info(`Customer status updated: ${customer.email} - Active: ${is_active} by ${req.user.email}`);

      res.json({
        success: true,
        message: `Customer ${is_active ? "activated" : "deactivated"} successfully`,
        customer: updatedCustomer.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Update customer status error:", error);
      throw error;
    }
  })
);

// GET /api/v1/customers/:id/projects - Get customer projects (Staff only)
router.get(
  "/:id/projects",
  customerLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateCustomerId,
  queryValidator("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  queryValidator("limit").optional().isInt({ min: 1, max: 50 }).withMessage("Limit must be between 1 and 50"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { page = 1, limit = 10 } = req.query;

    try {
      const customer = await User.findOne({
        where: { id, user_type: "customer" },
      });

      if (!customer) {
        throw new NotFoundError("Customer");
      }

      const { rows: projects, count } = await Project.findAndCountAll({
        where: { customer_id: id },
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        order: [["created_at", "DESC"]],
      });

      res.json({
        success: true,
        projects,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / limit),
        },
      });
    } catch (error) {
      logger.error("Get customer projects error:", error);
      throw error;
    }
  })
);

// GET /api/v1/customers/stats - Get customer statistics (Staff only)
router.get(
  "/stats",
  customerLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  asyncHandler(async (req, res) => {
    try {
      const { Op } = require("sequelize");
      const { sequelize } = require("../database/connection");

      const stats = await Promise.all([
        // Total customers
        User.count({ where: { user_type: "customer" } }),
        // Active customers
        User.count({ where: { user_type: "customer", is_active: true } }),
        // New customers this month
        User.count({
          where: {
            user_type: "customer",
            created_at: {
              [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            },
          },
        }),
        // Customers with projects
        User.count({
          where: { user_type: "customer" },
          include: [
            {
              model: Project,
              as: "projects",
              required: true,
            },
          ],
        }),
      ]);

      res.json({
        success: true,
        stats: {
          total: stats[0],
          active: stats[1],
          newThisMonth: stats[2],
          withProjects: stats[3],
        },
      });
    } catch (error) {
      logger.error("Get customer stats error:", error);
      throw error;
    }
  })
);

module.exports = router;
