// src/routes/customers.js
const express = require("express");
const { query } = require("../database/connection");
const { checkJwt, syncUser, requireStaff } = require("../middleware/auth");
const { asyncHandler } = require("../middleware/errorHandler");
const logger = require("../utils/logger");

const router = express.Router();

// Get all customers (Staff only)
router.get(
  "/",
  checkJwt,
  syncUser,
  requireStaff,
  asyncHandler(async (req, res) => {
    try {
      const { page = 1, limit = 20, search, isActive, sortBy = "created_at", sortOrder = "desc" } = req.query;

      const offset = (page - 1) * limit;
      let whereConditions = [];
      let params = [];
      let paramCount = 0;

      if (isActive !== undefined) {
        paramCount++;
        whereConditions.push(`is_active = $${paramCount}`);
        params.push(isActive === "true");
      }

      if (search) {
        paramCount++;
        whereConditions.push(`(first_name ILIKE $${paramCount} OR last_name ILIKE $${paramCount} OR email ILIKE $${paramCount} OR phone ILIKE $${paramCount})`);
        params.push(`%${search}%`);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : "";

      // Get total count
      const countResult = await query(`SELECT COUNT(*) as total FROM customers ${whereClause}`, params);
      const total = parseInt(countResult.rows[0].total);

      // Get customers
      const customersQuery = `
      SELECT * FROM customers 
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;
      params.push(limit, offset);

      const result = await query(customersQuery, params);

      res.json({
        success: true,
        data: {
          customers: result.rows,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      logger.error("Get customers error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch customers",
      });
    }
  })
);

module.exports = router;
