const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, param } = require('express-validator');
const { 
  checkJwt, 
  syncUser, 
  requireCustomer 
} = require('../middleware/auth');
const { Wishlist, Product, ProductImage, Category } = require('../models');
const { asyncHandler, handleValidationError, NotFoundError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for wishlist endpoints
const wishlistLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { error: "Too many requests, please try again later" },
});

// Validation middleware
const validateProductId = [
  param('productId').isUUID().withMessage('Product ID must be a valid UUID')
];

// GET /api/v1/wishlist - Get user's wishlist
router.get('/',
  wishlistLimiter,
  checkJwt,
  syncUser,
  requireCustomer,
  asyncHandler(async (req, res) => {
    const customerId = req.user.id;

    try {
      const wishlistItems = await Wishlist.findByCustomer(customerId);

      // Transform the data to match frontend expectations
      const items = wishlistItems.map(item => {
        const product = item.product;
        if (!product) return null;

        return {
          id: product.id,
          name: product.name,
          slug: product.slug,
          description: product.description,
          price: product.price,
          discount_price: product.discount_price,
          compare_price: product.price, // For showing original price
          image_url: product.images && product.images.length > 0 ? product.images[0].image_url : null,
          category: product.category ? product.category.name : null,
          brand: product.brand,
          stock_status: product.stock_status,
          is_active: product.is_active,
          created_at: item.created_at,
        };
      }).filter(Boolean);

      res.json({
        success: true,
        wishlist: items,
        count: items.length,
      });
    } catch (error) {
      logger.error('Get wishlist error:', error);
      throw error;
    }
  })
);

// POST /api/v1/wishlist/:productId - Add product to wishlist
router.post('/:productId',
  wishlistLimiter,
  checkJwt,
  syncUser,
  requireCustomer,
  validateProductId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { productId } = req.params;
    const customerId = req.user.id;

    try {
      // Check if product exists and is active
      const product = await Product.findOne({
        where: { 
          id: productId,
          is_active: true 
        }
      });

      if (!product) {
        throw new NotFoundError('Product');
      }

      // Add to wishlist
      const { wishlistItem, created } = await Wishlist.addItem(customerId, productId);

      if (!created) {
        return res.status(409).json({
          success: false,
          error: 'Product already in wishlist',
        });
      }

      logger.businessEvent('wishlist_item_added', {
        customerId,
        productId,
        productName: product.name,
      });

      res.status(201).json({
        success: true,
        message: 'Product added to wishlist',
        wishlistItem: wishlistItem.toSafeJSON(),
      });
    } catch (error) {
      logger.error('Add to wishlist error:', error);
      throw error;
    }
  })
);

// DELETE /api/v1/wishlist/:productId - Remove product from wishlist
router.delete('/:productId',
  wishlistLimiter,
  checkJwt,
  syncUser,
  requireCustomer,
  validateProductId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { productId } = req.params;
    const customerId = req.user.id;

    try {
      const deletedCount = await Wishlist.removeItem(customerId, productId);

      if (deletedCount === 0) {
        return res.status(404).json({
          success: false,
          error: 'Product not found in wishlist',
        });
      }

      logger.businessEvent('wishlist_item_removed', {
        customerId,
        productId,
      });

      res.json({
        success: true,
        message: 'Product removed from wishlist',
      });
    } catch (error) {
      logger.error('Remove from wishlist error:', error);
      throw error;
    }
  })
);

// DELETE /api/v1/wishlist - Clear entire wishlist
router.delete('/',
  wishlistLimiter,
  checkJwt,
  syncUser,
  requireCustomer,
  asyncHandler(async (req, res) => {
    const customerId = req.user.id;

    try {
      const deletedCount = await Wishlist.clearWishlist(customerId);

      logger.businessEvent('wishlist_cleared', {
        customerId,
        itemsRemoved: deletedCount,
      });

      res.json({
        success: true,
        message: `Wishlist cleared. ${deletedCount} items removed.`,
        itemsRemoved: deletedCount,
      });
    } catch (error) {
      logger.error('Clear wishlist error:', error);
      throw error;
    }
  })
);

// GET /api/v1/wishlist/check/:productId - Check if product is in wishlist
router.get('/check/:productId',
  wishlistLimiter,
  checkJwt,
  syncUser,
  requireCustomer,
  validateProductId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { productId } = req.params;
    const customerId = req.user.id;

    try {
      const isInWishlist = await Wishlist.isInWishlist(customerId, productId);

      res.json({
        success: true,
        isInWishlist,
      });
    } catch (error) {
      logger.error('Check wishlist error:', error);
      throw error;
    }
  })
);

// GET /api/v1/wishlist/count - Get wishlist count
router.get('/count',
  wishlistLimiter,
  checkJwt,
  syncUser,
  requireCustomer,
  asyncHandler(async (req, res) => {
    const customerId = req.user.id;

    try {
      const count = await Wishlist.getCount(customerId);

      res.json({
        success: true,
        count,
      });
    } catch (error) {
      logger.error('Get wishlist count error:', error);
      throw error;
    }
  })
);

module.exports = router;
