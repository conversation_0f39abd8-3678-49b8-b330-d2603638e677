const express = require("express");
const { body, param, query, validationResult } = require("express-validator");
const rateLimit = require("express-rate-limit");

const { checkJwt, syncUser, requireStaff, optionalAuth } = require("../middleware/auth");
const { WhatsAppConversation, WhatsAppMessage } = require("../models/WhatsApp");
const { User } = require("../models");
const { whatsappService } = require("../services/whatsappService");
const { asyncHandler, handleValidationError, NotFoundError } = require("../middleware/errorHandler");
const logger = require("../utils/logger");

const router = express.Router();

// Rate limiting for WhatsApp endpoints
const whatsappLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { error: "Too many WhatsApp requests, please try again later" },
});

// Webhook rate limiting (more restrictive)
const webhookLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // Allow up to 100 webhook calls per minute
  message: { error: "Webhook rate limit exceeded" },
});

// Validation middleware
const validatePhoneNumber = [
  body("phoneNumber")
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage("Invalid phone number format"),
];

const validateMessage = [body("message").trim().isLength({ min: 1, max: 4096 }).withMessage("Message must be between 1 and 4096 characters")];

const validateConversationId = [param("conversationId").isUUID().withMessage("Conversation ID must be a valid UUID")];

// GET /api/v1/whatsapp/webhook - Webhook verification
router.get("/webhook", (req, res) => {
  const mode = req.query["hub.mode"];
  const token = req.query["hub.verify_token"];
  const challenge = req.query["hub.challenge"];

  const verificationResult = whatsappService.verifyWebhook(mode, token, challenge);

  if (verificationResult) {
    res.status(200).send(challenge);
  } else {
    res.status(403).send("Verification failed");
  }
});

// POST /api/v1/whatsapp/webhook - Receive WhatsApp messages
router.post(
  "/webhook",
  webhookLimiter,
  asyncHandler(async (req, res) => {
    const body = req.body;

    try {
      const events = whatsappService.processWebhook(body);

      if (!events) {
        return res.status(200).send("OK");
      }

      // Process each event
      for (const event of events) {
        if (event.type === "message") {
          await processIncomingMessage(event);
        } else if (event.type === "status") {
          await processStatusUpdate(event);
        }
      }

      res.status(200).send("OK");
    } catch (error) {
      logger.error("Webhook processing error:", error);
      res.status(200).send("OK"); // Always return 200 to WhatsApp to avoid retries
    }
  })
);

// GET /api/v1/whatsapp/conversations - Get conversations (Staff only)
router.get(
  "/conversations",
  whatsappLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  query("limit").optional().isInt({ min: 1, max: 100 }).withMessage("Limit must be between 1 and 100"),
  query("search")
    .optional()
    .custom((value) => {
      if (value === "" || value === null || value === undefined) return true;
      return value.trim().length >= 1;
    })
    .withMessage("Search query cannot be empty"),
  query("status")
    .optional()
    .custom((value) => {
      if (value === "" || value === null || value === undefined) return true;
      return ["open", "in_progress", "resolved", "closed"].includes(value);
    })
    .withMessage("Invalid status"),
  query("assignedTo")
    .optional()
    .custom((value) => {
      if (value === "" || value === null || value === undefined) return true;
      return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value);
    })
    .withMessage("Assigned to must be a valid UUID"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 20, status, assignedTo, search, inquiry_type } = req.query;

    try {
      const { Op } = require("sequelize");
      const whereClause = {};

      if (status) {
        whereClause.status = status;
      }

      if (assignedTo) {
        whereClause.assigned_to = assignedTo;
      }

      if (inquiry_type) {
        whereClause.inquiry_type = inquiry_type;
      }

      // Add search functionality
      if (search) {
        whereClause[Op.or] = [{ whatsapp_number: { [Op.iLike]: `%${search}%` } }, { customer_name: { [Op.iLike]: `%${search}%` } }, { inquiry_type: { [Op.iLike]: `%${search}%` } }];
      }

      const { rows: conversations, count } = await WhatsAppConversation.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: "customer",
            attributes: ["id", "first_name", "last_name", "email"],
          },
          {
            model: User,
            as: "assignedStaff",
            attributes: ["id", "first_name", "last_name", "user_type"],
          },
        ],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        order: [["last_message_at", "DESC"]],
      });

      // Get unread count and last message for each conversation
      const conversationsWithDetails = await Promise.all(
        conversations.map(async (conversation) => {
          const [unreadCount, lastMessage] = await Promise.all([conversation.getUnreadCount(), conversation.getLastMessage()]);

          return {
            ...conversation.toJSON(),
            unreadCount,
            lastMessage: lastMessage
              ? {
                  id: lastMessage.id,
                  message_type: lastMessage.message_type,
                  message_content: lastMessage.message_content,
                  sender_type: lastMessage.sender_type,
                  sent_at: lastMessage.sent_at,
                }
              : null,
          };
        })
      );

      res.json({
        success: true,
        conversations: conversationsWithDetails,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / parseInt(limit)),
        },
      });
    } catch (error) {
      logger.error("Get conversations error:", error);
      throw error;
    }
  })
);

// GET /api/v1/whatsapp/conversations/:conversationId - Get conversation details
router.get(
  "/conversations/:conversationId",
  whatsappLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateConversationId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { conversationId } = req.params;

    try {
      const conversation = await WhatsAppConversation.findByPk(conversationId, {
        include: [
          {
            model: User,
            as: "customer",
            attributes: ["id", "first_name", "last_name", "email", "phone"],
          },
          {
            model: User,
            as: "assignedStaff",
            attributes: ["id", "first_name", "last_name", "user_type"],
          },
        ],
      });

      if (!conversation) {
        throw new NotFoundError("Conversation");
      }

      res.json({
        success: true,
        conversation: conversation.toJSON(),
      });
    } catch (error) {
      logger.error("Get conversation error:", error);
      throw error;
    }
  })
);

// GET /api/v1/whatsapp/conversations/:conversationId/messages - Get conversation messages
router.get(
  "/conversations/:conversationId/messages",
  whatsappLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateConversationId,
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  query("limit").optional().isInt({ min: 1, max: 100 }).withMessage("Limit must be between 1 and 100"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { conversationId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    try {
      // Verify conversation exists
      const conversation = await WhatsAppConversation.findByPk(conversationId);
      if (!conversation) {
        throw new NotFoundError("Conversation");
      }

      const { rows: messages, count } = await WhatsAppMessage.findByConversation(conversationId, parseInt(limit), (parseInt(page) - 1) * parseInt(limit));

      res.json({
        success: true,
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: Math.ceil(count / parseInt(limit)),
        },
      });
    } catch (error) {
      logger.error("Get conversation messages error:", error);
      throw error;
    }
  })
);

// POST /api/v1/whatsapp/conversations/:conversationId/messages - Send message
router.post(
  "/conversations/:conversationId/messages",
  whatsappLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateConversationId,
  validateMessage,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { conversationId } = req.params;
    const { message, messageType = "text", replyToMessageId } = req.body;

    try {
      // Verify conversation exists
      const conversation = await WhatsAppConversation.findByPk(conversationId);
      if (!conversation) {
        throw new NotFoundError("Conversation");
      }

      let whatsappResponse;

      // Send message via WhatsApp API
      switch (messageType) {
        case "text":
          whatsappResponse = await whatsappService.sendTextMessage(conversation.whatsapp_number, message, replyToMessageId);
          break;

        default:
          return res.status(400).json({
            error: "Unsupported message type",
          });
      }

      // Save message to database
      const savedMessage = await WhatsAppMessage.create({
        conversation_id: conversationId,
        whatsapp_message_id: whatsappResponse.messages[0]?.id,
        message_type: messageType,
        message_content: message,
        sender_type: "staff",
        sender_id: req.user.id,
        sender_name: req.user.getFullName(),
        delivery_status: "sent",
      });

      // Update conversation last message time
      await conversation.updateLastMessage();

      logger.businessEvent("whatsapp_message_sent", {
        conversationId,
        messageId: savedMessage.id,
        whatsappMessageId: whatsappResponse.messages[0]?.id,
        sentBy: req.user.id,
        recipient: conversation.whatsapp_number,
      });

      res.status(201).json({
        success: true,
        message: "Message sent successfully",
        data: {
          id: savedMessage.id,
          whatsapp_message_id: savedMessage.whatsapp_message_id,
          message_content: savedMessage.message_content,
          sent_at: savedMessage.sent_at,
        },
      });
    } catch (error) {
      logger.error("Send WhatsApp message error:", error);
      throw error;
    }
  })
);

// PUT /api/v1/whatsapp/conversations/:conversationId/assign - Assign conversation to staff
router.put(
  "/conversations/:conversationId/assign",
  whatsappLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateConversationId,
  body("assignedTo").optional().isUUID().withMessage("Assigned to must be a valid UUID"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { conversationId } = req.params;
    const { assignedTo } = req.body;

    try {
      const conversation = await WhatsAppConversation.findByPk(conversationId);
      if (!conversation) {
        throw new NotFoundError("Conversation");
      }

      // Verify assigned user exists and is staff
      if (assignedTo) {
        const assignedUser = await User.findByPk(assignedTo);
        if (!assignedUser || !assignedUser.isStaff()) {
          return res.status(400).json({
            error: "Assigned user must be a staff member",
          });
        }
      }

      await conversation.update({ assigned_to: assignedTo });

      logger.businessEvent("whatsapp_conversation_assigned", {
        conversationId,
        assignedTo,
        assignedBy: req.user.id,
      });

      res.json({
        success: true,
        message: assignedTo ? "Conversation assigned successfully" : "Conversation unassigned successfully",
      });
    } catch (error) {
      logger.error("Assign conversation error:", error);
      throw error;
    }
  })
);

// PUT /api/v1/whatsapp/conversations/:conversationId/status - Update conversation status
router.put(
  "/conversations/:conversationId/status",
  whatsappLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateConversationId,
  body("status").isIn(["open", "in_progress", "resolved", "closed"]).withMessage("Invalid status"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { conversationId } = req.params;
    const { status } = req.body;

    try {
      const conversation = await WhatsAppConversation.findByPk(conversationId);
      if (!conversation) {
        throw new NotFoundError("Conversation");
      }

      await conversation.update({ status });

      // If marking as resolved/closed, mark all messages as read
      if (status === "resolved" || status === "closed") {
        await conversation.markAsRead();
      }

      logger.businessEvent("whatsapp_conversation_status_updated", {
        conversationId,
        oldStatus: conversation.status,
        newStatus: status,
        updatedBy: req.user.id,
      });

      res.json({
        success: true,
        message: `Conversation status updated to ${status}`,
      });
    } catch (error) {
      logger.error("Update conversation status error:", error);
      throw error;
    }
  })
);

// POST /api/v1/whatsapp/conversations/:conversationId/mark-read - Mark messages as read
router.post(
  "/conversations/:conversationId/mark-read",
  whatsappLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateConversationId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { conversationId } = req.params;

    try {
      const conversation = await WhatsAppConversation.findByPk(conversationId);
      if (!conversation) {
        throw new NotFoundError("Conversation");
      }

      await conversation.markAsRead();

      res.json({
        success: true,
        message: "Messages marked as read",
      });
    } catch (error) {
      logger.error("Mark messages as read error:", error);
      throw error;
    }
  })
);

// POST /api/v1/whatsapp/send-template - Send template message
router.post(
  "/send-template",
  whatsappLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validatePhoneNumber,
  body("templateName").trim().isLength({ min: 1 }).withMessage("Template name is required"),
  body("templateParams").optional().isArray().withMessage("Template params must be an array"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { phoneNumber, templateName, templateParams = [] } = req.body;

    try {
      // Send template message
      const whatsappResponse = await whatsappService.sendTemplateMessage(phoneNumber, templateName, templateParams);

      // Find or create conversation
      let conversation = await WhatsAppConversation.findByPhoneNumber(phoneNumber);

      if (!conversation) {
        conversation = await WhatsAppConversation.create({
          whatsapp_number: phoneNumber,
          inquiry_type: "general",
          status: "open",
          assigned_to: req.user.id,
        });
      }

      // Save message to database
      const savedMessage = await WhatsAppMessage.create({
        conversation_id: conversation.id,
        whatsapp_message_id: whatsappResponse.messages[0]?.id,
        message_type: "template",
        template_name: templateName,
        template_params: templateParams,
        sender_type: "staff",
        sender_id: req.user.id,
        sender_name: req.user.getFullName(),
        delivery_status: "sent",
      });

      // Update conversation last message time
      await conversation.updateLastMessage();

      logger.businessEvent("whatsapp_template_sent", {
        conversationId: conversation.id,
        messageId: savedMessage.id,
        templateName,
        sentBy: req.user.id,
        recipient: phoneNumber,
      });

      res.status(201).json({
        success: true,
        message: "Template message sent successfully",
        data: {
          conversationId: conversation.id,
          messageId: savedMessage.id,
          whatsappMessageId: whatsappResponse.messages[0]?.id,
        },
      });
    } catch (error) {
      logger.error("Send template message error:", error);
      throw error;
    }
  })
);

// GET /api/v1/whatsapp/health - WhatsApp service health check
router.get(
  "/health",
  asyncHandler(async (req, res) => {
    try {
      const health = await whatsappService.healthCheck();

      res.status(health.status === "healthy" ? 200 : 503).json({
        service: "WhatsApp Business API",
        ...health,
      });
    } catch (error) {
      res.status(503).json({
        service: "WhatsApp Business API",
        status: "unhealthy",
        error: error.message,
        timestamp: new Date(),
      });
    }
  })
);

// Helper function to process incoming messages
async function processIncomingMessage(event) {
  try {
    let conversation = await WhatsAppConversation.findByPhoneNumber(event.from);

    // Create conversation if it doesn't exist
    if (!conversation) {
      conversation = await WhatsAppConversation.create({
        whatsapp_number: event.from,
        inquiry_type: "general",
        status: "open",
        last_message_at: event.timestamp,
      });
    }

    // Process media if present
    let mediaUrl = null;
    let mediaCaption = null;

    if (event.content.mediaId) {
      try {
        const uploadResult = await whatsappService.processAndUploadMedia(event.content.mediaId);
        mediaUrl = uploadResult.url;
        mediaCaption = event.content.caption;
      } catch (mediaError) {
        logger.error("Failed to process WhatsApp media:", mediaError);
      }
    }

    // Save message to database
    const savedMessage = await WhatsAppMessage.create({
      conversation_id: conversation.id,
      whatsapp_message_id: event.messageId,
      message_type: event.messageType,
      message_content: event.content.text || mediaCaption || "Media message",
      media_url: mediaUrl,
      media_caption: mediaCaption,
      sender_type: "customer",
      sender_name: conversation.customer_name,
      delivery_status: "delivered",
      sent_at: event.timestamp,
    });

    // Update conversation last message time
    await conversation.updateLastMessage(event.timestamp);

    logger.businessEvent("whatsapp_message_received", {
      conversationId: conversation.id,
      messageId: savedMessage.id,
      whatsappMessageId: event.messageId,
      from: event.from,
      messageType: event.messageType,
    });

    // Auto-respond for first-time customers (optional)
    if (!conversation.customer_id && event.messageType === "text") {
      try {
        await whatsappService.sendTextMessage(event.from, whatsappService.templates.welcome("there"));
      } catch (autoResponseError) {
        logger.warn("Failed to send auto-response:", autoResponseError);
      }
    }
  } catch (error) {
    logger.error("Failed to process incoming WhatsApp message:", error);
  }
}

// Helper function to process status updates
async function processStatusUpdate(event) {
  try {
    const message = await WhatsAppMessage.findOne({
      where: { whatsapp_message_id: event.messageId },
    });

    if (message) {
      await message.updateDeliveryStatus(event.status, event.errors?.[0]?.title);

      logger.businessEvent("whatsapp_message_status_updated", {
        messageId: message.id,
        whatsappMessageId: event.messageId,
        status: event.status,
        recipient: event.recipient,
      });
    }
  } catch (error) {
    logger.error("Failed to process WhatsApp status update:", error);
  }
}

module.exports = router;
