const express = require('express');
const { param, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

const { 
  checkJwt, 
  syncUser, 
  requireStaff, 
  requireCustomer 
} = require('../middleware/auth');
const { 
  s3Service,
  productImageUpload,
  categoryImageUpload,
  projectDocumentUpload,
  userAvatarUpload
} = require('../services/s3Service');
const { Product, ProductImage, Category } = require('../models');
const { asyncHandler, handleValidationError, NotFoundError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for upload endpoints
const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit each IP to 50 uploads per windowMs
  message: { error: 'Too many upload requests, please try again later' }
});

// Validation middleware
const validateProductId = [
  param('productId')
    .isUUID()
    .withMessage('Product ID must be a valid UUID')
];

const validateCategoryId = [
  param('categoryId')
    .isUUID()
    .withMessage('Category ID must be a valid UUID')
];

// POST /api/v1/upload/product-images/:productId - Upload product images (Admin only)
router.post('/product-images/:productId',
  uploadLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateProductId,
  handleValidationError,
  (req, res, next) => {
    productImageUpload.array('images', 10)(req, res, (err) => {
      if (err) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            error: 'File too large. Maximum size is 10MB per image.'
          });
        }
        if (err.code === 'LIMIT_FILE_COUNT') {
          return res.status(400).json({
            error: 'Too many files. Maximum 10 images per upload.'
          });
        }
        if (err.message.includes('Only image files')) {
          return res.status(400).json({
            error: 'Only image files (JPEG, PNG, WebP, GIF) are allowed.'
          });
        }
        return res.status(400).json({
          error: err.message || 'File upload error'
        });
      }
      next();
    });
  },
  asyncHandler(async (req, res) => {
    const { productId } = req.params;
    const files = req.files;

    if (!files || files.length === 0) {
      return res.status(400).json({
        error: 'No images provided'
      });
    }

    try {
      // Verify product exists
      const product = await Product.findByPk(productId);
      if (!product) {
        throw new NotFoundError('Product');
      }

      // Get current image count for sort order
      const currentImageCount = await ProductImage.count({
        where: { product_id: productId }
      });

      // Create ProductImage records
      const imagePromises = files.map(async (file, index) => {
        const isFirstImage = currentImageCount === 0 && index === 0;
        
        return await ProductImage.create({
          product_id: productId,
          image_url: file.location,
          alt_text: `${product.name} - Image ${currentImageCount + index + 1}`,
          is_primary: isFirstImage,
          sort_order: currentImageCount + index,
          file_name: file.originalname,
          file_size: file.size,
          mime_type: file.mimetype
        });
      });

      const productImages = await Promise.all(imagePromises);

      logger.businessEvent('product_images_uploaded', {
        productId,
        imageCount: files.length,
        uploadedBy: req.user.id
      });

      res.status(201).json({
        success: true,
        message: `${files.length} images uploaded successfully`,
        images: productImages.map(img => ({
          id: img.id,
          url: img.image_url,
          alt_text: img.alt_text,
          is_primary: img.is_primary,
          sort_order: img.sort_order
        }))
      });
    } catch (error) {
      // Clean up uploaded files if database operation fails
      if (files && files.length > 0) {
        const keys = files.map(file => s3Service.extractKeyFromUrl(file.location));
        s3Service.deleteFiles(keys).catch(deleteError => {
          logger.error('Failed to cleanup uploaded files:', deleteError);
        });
      }
      
      logger.error('Product images upload error:', error);
      throw error;
    }
  })
);

// POST /api/v1/upload/category-image/:categoryId - Upload category image (Admin only)
router.post('/category-image/:categoryId',
  uploadLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateCategoryId,
  handleValidationError,
  (req, res, next) => {
    categoryImageUpload.single('image')(req, res, (err) => {
      if (err) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            error: 'File too large. Maximum size is 10MB.'
          });
        }
        if (err.message.includes('Only image files')) {
          return res.status(400).json({
            error: 'Only image files (JPEG, PNG, WebP, GIF) are allowed.'
          });
        }
        return res.status(400).json({
          error: err.message || 'File upload error'
        });
      }
      next();
    });
  },
  asyncHandler(async (req, res) => {
    const { categoryId } = req.params;
    const file = req.file;

    if (!file) {
      return res.status(400).json({
        error: 'No image provided'
      });
    }

    try {
      // Verify category exists
      const category = await Category.findByPk(categoryId);
      if (!category) {
        // Clean up uploaded file
        const key = s3Service.extractKeyFromUrl(file.location);
        if (key) {
          await s3Service.deleteFile(key);
        }
        throw new NotFoundError('Category');
      }

      // Delete old image if exists
      if (category.image_url) {
        const oldKey = s3Service.extractKeyFromUrl(category.image_url);
        if (oldKey) {
          s3Service.deleteFile(oldKey).catch(error => {
            logger.warn('Failed to delete old category image:', error);
          });
        }
      }

      // Update category with new image URL
      await category.update({
        image_url: file.location
      });

      logger.businessEvent('category_image_uploaded', {
        categoryId,
        categoryName: category.name,
        uploadedBy: req.user.id
      });

      res.status(201).json({
        success: true,
        message: 'Category image uploaded successfully',
        image: {
          url: file.location,
          filename: file.originalname,
          size: file.size
        }
      });
    } catch (error) {
      // Clean up uploaded file if database operation fails
      if (file) {
        const key = s3Service.extractKeyFromUrl(file.location);
        if (key) {
          s3Service.deleteFile(key).catch(deleteError => {
            logger.error('Failed to cleanup uploaded file:', deleteError);
          });
        }
      }
      
      logger.error('Category image upload error:', error);
      throw error;
    }
  })
);

// POST /api/v1/upload/user-avatar - Upload user avatar
router.post('/user-avatar',
  uploadLimiter,
  checkJwt,
  syncUser,
  (req, res, next) => {
    userAvatarUpload.single('avatar')(req, res, (err) => {
      if (err) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return res.status(400).json({
            error: 'File too large. Maximum size is 2MB for avatars.'
          });
        }
        if (err.message.includes('Only image files')) {
          return res.status(400).json({
            error: 'Only image files (JPEG, PNG, WebP, GIF) are allowed.'
          });
        }
        return res.status(400).json({
          error: err.message || 'File upload error'
        });
      }
      next();
    });
  },
  asyncHandler(async (req, res) => {
    const file = req.file;
    const user = req.user;

    if (!file) {
      return res.status(400).json({
        error: 'No avatar image provided'
      });
    }

    try {
      // Delete old avatar if exists
      if (user.avatar_url) {
        const oldKey = s3Service.extractKeyFromUrl(user.avatar_url);
        if (oldKey) {
          s3Service.deleteFile(oldKey).catch(error => {
            logger.warn('Failed to delete old avatar:', error);
          });
        }
      }

      // Update user with new avatar URL
      await user.update({
        avatar_url: file.location
      });

      logger.businessEvent('user_avatar_uploaded', {
        userId: user.id,
        userEmail: user.email
      });

      res.status(201).json({
        success: true,
        message: 'Avatar uploaded successfully',
        avatar: {
          url: file.location,
          filename: file.originalname,
          size: file.size
        }
      });
    } catch (error) {
      // Clean up uploaded file if database operation fails
      if (file) {
        const key = s3Service.extractKeyFromUrl(file.location);
        if (key) {
          s3Service.deleteFile(key).catch(deleteError => {
            logger.error('Failed to cleanup uploaded avatar:', deleteError);
          });
        }
      }
      
      logger.error('User avatar upload error:', error);
      throw error;
    }
  })
);

// DELETE /api/v1/upload/product-image/:imageId - Delete product image (Admin only)
router.delete('/product-image/:imageId',
  uploadLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  param('imageId').isUUID().withMessage('Image ID must be a valid UUID'),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { imageId } = req.params;

    try {
      const productImage = await ProductImage.findByPk(imageId);
      
      if (!productImage) {
        throw new NotFoundError('Product image');
      }

      // Delete from S3
      const key = s3Service.extractKeyFromUrl(productImage.image_url);
      if (key) {
        await s3Service.deleteFile(key);
      }

      // If this was the primary image, set another image as primary
      if (productImage.is_primary) {
        const nextImage = await ProductImage.findOne({
          where: {
            product_id: productImage.product_id,
            id: { [require('sequelize').Op.ne]: imageId }
          },
          order: [['sort_order', 'ASC']]
        });

        if (nextImage) {
          await nextImage.update({ is_primary: true });
        }
      }

      // Delete from database
      await productImage.destroy();

      logger.businessEvent('product_image_deleted', {
        imageId,
        productId: productImage.product_id,
        deletedBy: req.user.id
      });

      res.json({
        success: true,
        message: 'Product image deleted successfully'
      });
    } catch (error) {
      logger.error('Delete product image error:', error);
      throw error;
    }
  })
);

// PATCH /api/v1/upload/product-image/:imageId/primary - Set image as primary (Admin only)
router.patch('/product-image/:imageId/primary',
  uploadLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  param('imageId').isUUID().withMessage('Image ID must be a valid UUID'),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { imageId } = req.params;

    try {
      const productImage = await ProductImage.findByPk(imageId);
      
      if (!productImage) {
        throw new NotFoundError('Product image');
      }

      // Unset other primary images for the same product
      await ProductImage.update(
        { is_primary: false },
        {
          where: {
            product_id: productImage.product_id,
            id: { [require('sequelize').Op.ne]: imageId }
          }
        }
      );

      // Set this image as primary
      await productImage.update({ is_primary: true });

      logger.businessEvent('product_image_primary_set', {
        imageId,
        productId: productImage.product_id,
        updatedBy: req.user.id
      });

      res.json({
        success: true,
        message: 'Image set as primary successfully'
      });
    } catch (error) {
      logger.error('Set primary image error:', error);
      throw error;
    }
  })
);

// PUT /api/v1/upload/product-images/:productId/reorder - Reorder product images (Admin only)
router.put('/product-images/:productId/reorder',
  uploadLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateProductId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { productId } = req.params;
    const { imageIds } = req.body;

    if (!Array.isArray(imageIds) || imageIds.length === 0) {
      return res.status(400).json({
        error: 'imageIds must be a non-empty array'
      });
    }

    try {
      // Verify product exists
      const product = await Product.findByPk(productId);
      if (!product) {
        throw new NotFoundError('Product');
      }

      // Verify all images belong to this product
      const images = await ProductImage.findAll({
        where: {
          product_id: productId,
          id: imageIds
        }
      });

      if (images.length !== imageIds.length) {
        return res.status(400).json({
          error: 'Some image IDs are invalid or do not belong to this product'
        });
      }

      // Update sort order for each image
      const updatePromises = imageIds.map((imageId, index) => {
        return ProductImage.update(
          { sort_order: index },
          { where: { id: imageId } }
        );
      });

      await Promise.all(updatePromises);

      logger.businessEvent('product_images_reordered', {
        productId,
        imageCount: imageIds.length,
        updatedBy: req.user.id
      });

      res.json({
        success: true,
        message: 'Product images reordered successfully'
      });
    } catch (error) {
      logger.error('Reorder product images error:', error);
      throw error;
    }
  })
);

// GET /api/v1/upload/health - S3 health check
router.get('/health',
  asyncHandler(async (req, res) => {
    try {
      const { s3HealthCheck } = require('../services/s3Service');
      const health = await s3HealthCheck();
      
      res.status(health.status === 'healthy' ? 200 : 503).json({
        service: 'S3',
        ...health
      });
    } catch (error) {
      res.status(503).json({
        service: 'S3',
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      });
    }
  })
);

module.exports = router;