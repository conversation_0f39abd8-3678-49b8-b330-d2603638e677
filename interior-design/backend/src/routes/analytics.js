const express = require('express');
const { query, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

const { checkJwt, syncUser, requireStaff } = require('../middleware/auth');
const { asyncHandler, handleValidationError } = require('../middleware/errorHandler');
const analyticsService = require('../services/analyticsService');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for analytics endpoints
const analyticsLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit each IP to 50 requests per windowMs
  message: { error: 'Too many analytics requests, please try again later' }
});

// Validation middleware
const validateDateRange = [
  query('dateRange')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Date range must be between 1 and 365 days')
];

const validateAnalyticsType = [
  query('type')
    .optional()
    .isIn(['dashboard', 'customers', 'products', 'projects', 'whatsapp', 'revenue'])
    .withMessage('Invalid analytics type')
];

// GET /api/v1/analytics/dashboard - Get dashboard statistics
router.get('/dashboard',
  analyticsLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateDateRange,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { dateRange = 30 } = req.query;

    try {
      const stats = await analyticsService.getDashboardStats(parseInt(dateRange));

      res.json({
        success: true,
        data: stats,
        dateRange: parseInt(dateRange),
        generatedAt: new Date()
      });
    } catch (error) {
      logger.error('Dashboard analytics error:', error);
      throw error;
    }
  })
);

// GET /api/v1/analytics/customers - Get customer analytics
router.get('/customers',
  analyticsLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateDateRange,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { dateRange = 30 } = req.query;

    try {
      const analytics = await analyticsService.getCustomerAnalytics(parseInt(dateRange));

      res.json({
        success: true,
        data: analytics,
        dateRange: parseInt(dateRange),
        generatedAt: new Date()
      });
    } catch (error) {
      logger.error('Customer analytics error:', error);
      throw error;
    }
  })
);

// GET /api/v1/analytics/products - Get product analytics
router.get('/products',
  analyticsLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateDateRange,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { dateRange = 30 } = req.query;

    try {
      const analytics = await analyticsService.getProductAnalytics(parseInt(dateRange));

      res.json({
        success: true,
        data: analytics,
        dateRange: parseInt(dateRange),
        generatedAt: new Date()
      });
    } catch (error) {
      logger.error('Product analytics error:', error);
      throw error;
    }
  })
);

// GET /api/v1/analytics/projects - Get project analytics
router.get('/projects',
  analyticsLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateDateRange,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { dateRange = 30 } = req.query;

    try {
      const analytics = await analyticsService.getProjectAnalytics(parseInt(dateRange));

      res.json({
        success: true,
        data: analytics,
        dateRange: parseInt(dateRange),
        generatedAt: new Date()
      });
    } catch (error) {
      logger.error('Project analytics error:', error);
      throw error;
    }
  })
);

// GET /api/v1/analytics/whatsapp - Get WhatsApp analytics
router.get('/whatsapp',
  analyticsLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateDateRange,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { dateRange = 30 } = req.query;

    try {
      const analytics = await analyticsService.getWhatsAppAnalytics(parseInt(dateRange));

      res.json({
        success: true,
        data: analytics,
        dateRange: parseInt(dateRange),
        generatedAt: new Date()
      });
    } catch (error) {
      logger.error('WhatsApp analytics error:', error);
      throw error;
    }
  })
);

// GET /api/v1/analytics/revenue - Get revenue analytics
router.get('/revenue',
  analyticsLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateDateRange,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { dateRange = 30 } = req.query;

    try {
      const analytics = await analyticsService.getRevenueAnalytics(parseInt(dateRange));

      res.json({
        success: true,
        data: analytics,
        dateRange: parseInt(dateRange),
        generatedAt: new Date()
      });
    } catch (error) {
      logger.error('Revenue analytics error:', error);
      throw error;
    }
  })
);

// GET /api/v1/analytics/comprehensive - Get comprehensive analytics report
router.get('/comprehensive',
  analyticsLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateDateRange,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { dateRange = 30 } = req.query;

    try {
      const report = await analyticsService.getComprehensiveReport(parseInt(dateRange));

      res.json({
        success: true,
        data: report
      });
    } catch (error) {
      logger.error('Comprehensive analytics error:', error);
      throw error;
    }
  })
);

// POST /api/v1/analytics/export - Export analytics data
router.post('/export',
  analyticsLimiter,
  checkJwt,
  syncUser,
  requireStaff,
  validateAnalyticsType,
  validateDateRange,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { type = 'dashboard', dateRange = 30 } = req.body;

    try {
      let data;
      
      switch (type) {
        case 'dashboard':
          data = await analyticsService.getDashboardStats(parseInt(dateRange));
          break;
        case 'customers':
          data = await analyticsService.getCustomerAnalytics(parseInt(dateRange));
          break;
        case 'products':
          data = await analyticsService.getProductAnalytics(parseInt(dateRange));
          break;
        case 'projects':
          data = await analyticsService.getProjectAnalytics(parseInt(dateRange));
          break;
        case 'whatsapp':
          data = await analyticsService.getWhatsAppAnalytics(parseInt(dateRange));
          break;
        case 'revenue':
          data = await analyticsService.getRevenueAnalytics(parseInt(dateRange));
          break;
        default:
          data = await analyticsService.getComprehensiveReport(parseInt(dateRange));
      }

      // Set headers for file download
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="analytics-${type}-${new Date().toISOString().split('T')[0]}.json"`);

      res.json({
        success: true,
        type,
        dateRange: parseInt(dateRange),
        exportedAt: new Date(),
        data
      });
    } catch (error) {
      logger.error('Analytics export error:', error);
      throw error;
    }
  })
);

module.exports = router;
