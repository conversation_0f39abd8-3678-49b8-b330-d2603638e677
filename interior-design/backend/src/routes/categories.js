const express = require("express");
const { body, param, query, validationResult } = require("express-validator");
const rateLimit = require("express-rate-limit");

const { checkJwt, syncUser, requireStaff, optionalAuth } = require("../middleware/auth");
const { Category, Product } = require("../models");
const { asyncHand<PERSON>, handleValidationError, NotFoundError } = require("../middleware/errorHandler");
const logger = require("../utils/logger");

const router = express.Router();

// Rate limiting for category endpoints
const categoryLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { error: "Too many requests, please try again later" },
});

// Validation middleware
const validateCategory = [
  body("name").trim().isLength({ min: 1, max: 100 }).withMessage("Name must be between 1 and 100 characters"),
  body("slug")
    .optional()
    .trim()
    .matches(/^[a-z0-9-]+$/)
    .withMessage("Slug must contain only lowercase letters, numbers, and hyphens"),
  body("description").optional().trim().isLength({ max: 1000 }).withMessage("Description must be less than 1000 characters"),
  body("parent_id")
    .optional({ nullable: true })
    .custom((value) => {
      if (value === null || value === undefined || value === "") return true;
      return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value);
    })
    .withMessage("Parent ID must be a valid UUID or null"),
  body("sort_order").optional().isInt({ min: 0 }).withMessage("Sort order must be a positive integer"),
  body("is_active").optional().isBoolean().withMessage("is_active must be a boolean"),
  body("is_room_type").optional().isBoolean().withMessage("is_room_type must be a boolean"),
  body("meta_title").optional().trim().isLength({ max: 255 }).withMessage("Meta title must be less than 255 characters"),
  body("meta_description").optional().trim().isLength({ max: 500 }).withMessage("Meta description must be less than 500 characters"),
];

const validateCategoryId = [param("id").isUUID().withMessage("Category ID must be a valid UUID")];

// GET /api/v1/categories - Get all categories (public endpoint)
router.get(
  "/",
  categoryLimiter,
  optionalAuth,
  query("tree").optional().isBoolean().withMessage("Tree parameter must be boolean"),
  query("parent").optional().isUUID().withMessage("Parent must be a valid UUID"),
  query("active").optional().isBoolean().withMessage("Active parameter must be boolean"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { tree, parent, active = "true" } = req.query;

    try {
      let categories;

      if (tree === "true") {
        // Return hierarchical tree structure
        categories = await Category.buildCategoryTree();
      } else if (parent) {
        // Return children of specific parent
        categories = await Category.findAll({
          where: {
            parent_id: parent,
            is_active: active === "true",
          },
          order: [
            ["sort_order", "ASC"],
            ["name", "ASC"],
          ],
        });
      } else {
        // Return all categories with product counts
        categories = await Category.findWithProductCount({ Product });
      }

      res.json({
        success: true,
        categories,
        count: Array.isArray(categories) ? categories.length : 0,
      });
    } catch (error) {
      logger.error("Get categories error:", error);
      throw error;
    }
  })
);

// GET /api/v1/categories/roots - Get root categories with children
router.get(
  "/roots",
  categoryLimiter,
  optionalAuth,
  asyncHandler(async (req, res) => {
    try {
      const categories = await Category.findRootCategories();

      res.json({
        success: true,
        categories,
        count: categories.length,
      });
    } catch (error) {
      logger.error("Get root categories error:", error);
      throw error;
    }
  })
);

// GET /api/v1/categories/:id - Get category by ID
router.get(
  "/:id",
  categoryLimiter,
  optionalAuth,
  validateCategoryId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    try {
      const category = await Category.findByPk(id, {
        include: [
          {
            model: Category,
            as: "parent",
          },
          {
            model: Category,
            as: "children",
            where: { is_active: true },
            required: false,
            order: [
              ["sort_order", "ASC"],
              ["name", "ASC"],
            ],
          },
        ],
      });

      if (!category) {
        throw new NotFoundError("Category");
      }

      res.json({
        success: true,
        category: category.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Get category error:", error);
      throw error;
    }
  })
);

// GET /api/v1/categories/slug/:slug - Get category by slug
router.get(
  "/slug/:slug",
  categoryLimiter,
  optionalAuth,
  param("slug")
    .matches(/^[a-z0-9-]+$/)
    .withMessage("Invalid slug format"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { slug } = req.params;

    try {
      const category = await Category.findBySlug(slug);

      if (!category) {
        throw new NotFoundError("Category");
      }

      res.json({
        success: true,
        category: category.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Get category by slug error:", error);
      throw error;
    }
  })
);

// POST /api/v1/categories - Create new category (Admin only)
router.post(
  "/",
  checkJwt,
  syncUser,
  requireStaff,
  validateCategory,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { name, slug, description, parent_id, image_url, sort_order, meta_title, meta_description } = req.body;

    try {
      // Check if parent exists (if provided)
      if (parent_id) {
        const parentCategory = await Category.findByPk(parent_id);
        if (!parentCategory) {
          return res.status(400).json({
            error: "Parent category not found",
          });
        }
      }

      // Check for duplicate slug
      if (slug) {
        const existingCategory = await Category.findOne({ where: { slug } });
        if (existingCategory) {
          return res.status(409).json({
            error: "Category with this slug already exists",
          });
        }
      }

      const category = await Category.create({
        name,
        slug,
        description,
        parent_id,
        image_url,
        sort_order: sort_order || 0,
        meta_title,
        meta_description,
      });

      logger.businessEvent("category_created", {
        categoryId: category.id,
        categoryName: category.name,
        createdBy: req.user.id,
      });

      res.status(201).json({
        success: true,
        message: "Category created successfully",
        category: category.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Create category error:", error);
      throw error;
    }
  })
);

// PUT /api/v1/categories/:id - Update category (Admin only)
router.put(
  "/:id",
  checkJwt,
  syncUser,
  requireStaff,
  validateCategoryId,
  validateCategory,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { name, slug, description, parent_id, image_url, sort_order, is_active, meta_title, meta_description } = req.body;

    try {
      const category = await Category.findByPk(id);

      if (!category) {
        throw new NotFoundError("Category");
      }

      // Check if parent exists and prevent circular reference
      if (parent_id) {
        if (parent_id === id) {
          return res.status(400).json({
            error: "Category cannot be its own parent",
          });
        }

        const parentCategory = await Category.findByPk(parent_id);
        if (!parentCategory) {
          return res.status(400).json({
            error: "Parent category not found",
          });
        }
      }

      // Check for duplicate slug (excluding current category)
      if (slug && slug !== category.slug) {
        const existingCategory = await Category.findOne({
          where: {
            slug,
            id: { [require("sequelize").Op.ne]: id },
          },
        });
        if (existingCategory) {
          return res.status(409).json({
            error: "Category with this slug already exists",
          });
        }
      }

      const updatedCategory = await category.update({
        name: name || category.name,
        slug: slug || category.slug,
        description: description !== undefined ? description : category.description,
        parent_id: parent_id !== undefined ? parent_id : category.parent_id,
        image_url: image_url !== undefined ? image_url : category.image_url,
        sort_order: sort_order !== undefined ? sort_order : category.sort_order,
        is_active: is_active !== undefined ? is_active : category.is_active,
        meta_title: meta_title !== undefined ? meta_title : category.meta_title,
        meta_description: meta_description !== undefined ? meta_description : category.meta_description,
      });

      logger.businessEvent("category_updated", {
        categoryId: category.id,
        categoryName: category.name,
        updatedBy: req.user.id,
      });

      res.json({
        success: true,
        message: "Category updated successfully",
        category: updatedCategory.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Update category error:", error);
      throw error;
    }
  })
);

// DELETE /api/v1/categories/:id - Delete category (Admin only)
router.delete(
  "/:id",
  checkJwt,
  syncUser,
  requireStaff,
  validateCategoryId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    try {
      const category = await Category.findByPk(id, {
        include: [
          {
            model: Category,
            as: "children",
          },
        ],
      });

      if (!category) {
        throw new NotFoundError("Category");
      }

      // Check if category has children
      if (category.children && category.children.length > 0) {
        return res.status(400).json({
          error: "Cannot delete category with subcategories. Please delete or move subcategories first.",
        });
      }

      // Check if category has products
      const productCount = await Product.count({
        where: { category_id: id },
      });

      if (productCount > 0) {
        return res.status(400).json({
          error: `Cannot delete category with ${productCount} products. Please move or delete products first.`,
        });
      }

      await category.destroy();

      logger.businessEvent("category_deleted", {
        categoryId: id,
        categoryName: category.name,
        deletedBy: req.user.id,
      });

      res.json({
        success: true,
        message: "Category deleted successfully",
      });
    } catch (error) {
      logger.error("Delete category error:", error);
      throw error;
    }
  })
);

// PATCH /api/v1/categories/:id/toggle-status - Toggle category status (Admin only)
router.patch(
  "/:id/toggle-status",
  checkJwt,
  syncUser,
  requireStaff,
  validateCategoryId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    try {
      const category = await Category.findByPk(id);

      if (!category) {
        throw new NotFoundError("Category");
      }

      const updatedCategory = await category.update({
        is_active: !category.is_active,
      });

      logger.businessEvent("category_status_toggled", {
        categoryId: id,
        categoryName: category.name,
        newStatus: updatedCategory.is_active,
        updatedBy: req.user.id,
      });

      res.json({
        success: true,
        message: `Category ${updatedCategory.is_active ? "activated" : "deactivated"} successfully`,
        category: updatedCategory.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Toggle category status error:", error);
      throw error;
    }
  })
);

module.exports = router;
