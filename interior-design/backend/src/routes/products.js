const express = require("express");
const { body, param, query, validationResult } = require("express-validator");
const rateLimit = require("express-rate-limit");

const { checkJwt, syncUser, requireStaff, optionalAuth } = require("../middleware/auth");
const { Product, Category, ProductImage } = require("../models");
const { asyncHandler, handleValidationError, NotFoundError } = require("../middleware/errorHandler");
const logger = require("../utils/logger");

const router = express.Router();

// Rate limiting
const productLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // limit each IP to 200 requests per windowMs
  message: { error: "Too many requests, please try again later" },
});

// Validation middleware
const validateProduct = [
  body("name").trim().isLength({ min: 1, max: 255 }).withMessage("Name must be between 1 and 255 characters"),
  body("slug")
    .optional()
    .trim()
    .matches(/^[a-z0-9-]+$/)
    .withMessage("Slug must contain only lowercase letters, numbers, and hyphens"),
  body("description").optional().trim().isLength({ max: 5000 }).withMessage("Description must be less than 5000 characters"),
  body("short_description").optional().trim().isLength({ max: 500 }).withMessage("Short description must be less than 500 characters"),
  body("price").optional().isFloat({ min: 0 }).withMessage("Price must be a positive number"),
  body("discount_price").optional().isFloat({ min: 0 }).withMessage("Discount price must be a positive number"),
  body("category_id").optional().isUUID().withMessage("Category ID must be a valid UUID"),
  body("stock_status").optional().isIn(["in_stock", "out_of_stock", "on_order"]).withMessage("Invalid stock status"),
];

const validateProductId = [param("id").isUUID().withMessage("Product ID must be a valid UUID")];

// GET /api/v1/products - Get all products with filtering and pagination
router.get(
  "/",
  productLimiter,
  optionalAuth,
  query("page").optional().isInt({ min: 1 }).withMessage("Page must be a positive integer"),
  query("limit").optional().isInt({ min: 1, max: 100 }).withMessage("Limit must be between 1 and 100"),
  query("category").optional().isUUID().withMessage("Category must be a valid UUID"),
  query("featured").optional().isBoolean().withMessage("Featured must be boolean"),
  query("search").optional().trim().isLength({ min: 1 }).withMessage("Search query cannot be empty"),
  query("min_price").optional().isFloat({ min: 0 }).withMessage("Min price must be positive"),
  query("max_price").optional().isFloat({ min: 0 }).withMessage("Max price must be positive"),
  query("sort").optional().isIn(["name", "price", "created_at", "view_count"]).withMessage("Invalid sort field"),
  query("order").optional().isIn(["ASC", "DESC"]).withMessage("Order must be ASC or DESC"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 20, category, featured, search, min_price, max_price, brand, room_type, sort = "created_at", order = "DESC" } = req.query;

    try {
      let products;
      let totalCount;

      if (featured === "true") {
        // Get featured products
        products = await Product.findFeatured(parseInt(limit), { Category, ProductImage });
        totalCount = products.length;
      } else if (search) {
        // Search products
        const result = await Product.search(
          search,
          {
            page: parseInt(page),
            limit: parseInt(limit),
            categoryId: category,
            minPrice: min_price,
            maxPrice: max_price,
            brand,
            roomType: room_type,
          },
          { Category, ProductImage }
        );
        products = result.rows;
        totalCount = result.count;
      } else if (category) {
        // Get products by category
        const result = await Product.findByCategory(
          category,
          {
            page: parseInt(page),
            limit: parseInt(limit),
            sortBy: sort,
            sortOrder: order,
          },
          { Category, ProductImage }
        );
        products = result.rows;
        totalCount = result.count;
      } else {
        // Get all products with filters
        const { Op } = require("sequelize");
        const whereClause = { is_active: true };

        // Apply price filter
        if (min_price || max_price) {
          whereClause.price = {};
          if (min_price) whereClause.price[Op.gte] = parseFloat(min_price);
          if (max_price) whereClause.price[Op.lte] = parseFloat(max_price);
        }

        // Apply brand filter
        if (brand) {
          whereClause.brand = { [Op.iLike]: `%${brand}%` };
        }

        // Apply room type filter
        if (room_type) {
          whereClause.room_type = { [Op.iLike]: `%${room_type}%` };
        }

        const result = await Product.findAndCountAll({
          where: whereClause,
          include: [
            {
              model: Category,
              as: "category",
            },
            {
              model: ProductImage,
              as: "images",
              where: { is_primary: true },
              required: false,
            },
          ],
          limit: parseInt(limit),
          offset: (parseInt(page) - 1) * parseInt(limit),
          order: [[sort, order]],
        });

        products = result.rows;
        totalCount = result.count;
      }

      const totalPages = Math.ceil(totalCount / parseInt(limit));

      res.json({
        success: true,
        products: products.map((product) => product.toSafeJSON()),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalCount,
          pages: totalPages,
          hasNext: parseInt(page) < totalPages,
          hasPrev: parseInt(page) > 1,
        },
      });
    } catch (error) {
      logger.error("Get products error:", error);
      throw error;
    }
  })
);

// GET /api/v1/products/featured - Get featured products
router.get(
  "/featured",
  productLimiter,
  optionalAuth,
  query("limit").optional().isInt({ min: 1, max: 50 }).withMessage("Limit must be between 1 and 50"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { limit = 10 } = req.query;

    try {
      const products = await Product.findFeatured(parseInt(limit), { Category, ProductImage });

      res.json({
        success: true,
        products: products.map((product) => product.toSafeJSON()),
        count: products.length,
      });
    } catch (error) {
      logger.error("Get featured products error:", error);
      throw error;
    }
  })
);

// GET /api/v1/products/filters - Get available filter options
router.get(
  "/filters",
  productLimiter,
  optionalAuth,
  asyncHandler(async (req, res) => {
    try {
      const { Op } = require("sequelize");

      // Get unique filter values
      const [brands, roomTypes, styles, materials, priceRange] = await Promise.all([
        Product.findAll({
          attributes: [[require("sequelize").fn("DISTINCT", require("sequelize").col("brand")), "brand"]],
          where: { brand: { [Op.ne]: null }, is_active: true },
          raw: true,
        }),
        Product.findAll({
          attributes: [[require("sequelize").fn("DISTINCT", require("sequelize").col("room_type")), "room_type"]],
          where: { room_type: { [Op.ne]: null }, is_active: true },
          raw: true,
        }),
        Product.findAll({
          attributes: [[require("sequelize").fn("DISTINCT", require("sequelize").col("style")), "style"]],
          where: { style: { [Op.ne]: null }, is_active: true },
          raw: true,
        }),
        Product.findAll({
          attributes: [[require("sequelize").fn("DISTINCT", require("sequelize").col("material")), "material"]],
          where: { material: { [Op.ne]: null }, is_active: true },
          raw: true,
        }),
        Product.findOne({
          attributes: [
            [require("sequelize").fn("MIN", require("sequelize").col("price")), "min_price"],
            [require("sequelize").fn("MAX", require("sequelize").col("price")), "max_price"],
          ],
          where: { price: { [Op.ne]: null }, is_active: true },
          raw: true,
        }),
      ]);

      res.json({
        success: true,
        filters: {
          brands: brands
            .map((b) => b.brand)
            .filter(Boolean)
            .sort(),
          roomTypes: roomTypes
            .map((r) => r.room_type)
            .filter(Boolean)
            .sort(),
          styles: styles
            .map((s) => s.style)
            .filter(Boolean)
            .sort(),
          materials: materials
            .map((m) => m.material)
            .filter(Boolean)
            .sort(),
          priceRange: {
            min: priceRange?.min_price || 0,
            max: priceRange?.max_price || 0,
          },
        },
      });
    } catch (error) {
      logger.error("Get product filters error:", error);
      throw error;
    }
  })
);

// GET /api/v1/products/:id - Get product by ID
router.get(
  "/:id",
  productLimiter,
  optionalAuth,
  validateProductId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    try {
      const product = await Product.findByPk(id, {
        include: [
          {
            model: Category,
            as: "category",
          },
          {
            model: ProductImage,
            as: "images",
            order: [
              ["is_primary", "DESC"],
              ["sort_order", "ASC"],
            ],
          },
        ],
      });

      if (!product || !product.is_active) {
        throw new NotFoundError("Product");
      }

      // Increment view count
      await product.incrementViewCount();

      res.json({
        success: true,
        product: product.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Get product error:", error);
      throw error;
    }
  })
);

// GET /api/v1/products/slug/:slug - Get product by slug
router.get(
  "/slug/:slug",
  productLimiter,
  optionalAuth,
  param("slug")
    .matches(/^[a-z0-9-]+$/)
    .withMessage("Invalid slug format"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { slug } = req.params;

    try {
      const product = await Product.findBySlug(slug, true, { Category, ProductImage });

      if (!product) {
        throw new NotFoundError("Product");
      }

      // Increment view count
      await product.incrementViewCount();

      res.json({
        success: true,
        product: product.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Get product by slug error:", error);
      throw error;
    }
  })
);

// POST /api/v1/products - Create new product (Admin only)
router.post(
  "/",
  checkJwt,
  syncUser,
  requireStaff,
  validateProduct,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const {
      name,
      slug,
      description,
      short_description,
      sku,
      price,
      discount_price,
      category_id,
      brand,
      material,
      dimensions,
      color,
      style,
      room_type,
      is_featured,
      stock_status,
      weight,
      warranty_period,
      assembly_required,
      meta_title,
      meta_description,
    } = req.body;

    try {
      // Validate category exists
      if (category_id) {
        const category = await Category.findByPk(category_id);
        if (!category) {
          return res.status(400).json({
            error: "Category not found",
          });
        }
      }

      // Check for duplicate slug
      if (slug) {
        const existingProduct = await Product.findOne({ where: { slug } });
        if (existingProduct) {
          return res.status(409).json({
            error: "Product with this slug already exists",
          });
        }
      }

      // Check for duplicate SKU
      if (sku) {
        const existingProduct = await Product.findOne({ where: { sku } });
        if (existingProduct) {
          return res.status(409).json({
            error: "Product with this SKU already exists",
          });
        }
      }

      // Validate discount price
      if (discount_price && price && discount_price >= price) {
        return res.status(400).json({
          error: "Discount price must be less than regular price",
        });
      }

      const product = await Product.create({
        name,
        slug,
        description,
        short_description,
        sku,
        price,
        discount_price,
        category_id,
        brand,
        material,
        dimensions,
        color,
        style,
        room_type,
        is_featured: is_featured || false,
        stock_status: stock_status || "in_stock",
        weight,
        warranty_period,
        assembly_required: assembly_required || false,
        meta_title,
        meta_description,
        created_by: req.user.id,
      });

      logger.businessEvent("product_created", {
        productId: product.id,
        productName: product.name,
        createdBy: req.user.id,
      });

      res.status(201).json({
        success: true,
        message: "Product created successfully",
        product: product.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Create product error:", error);
      throw error;
    }
  })
);

// PUT /api/v1/products/:id - Update product (Admin only)
router.put(
  "/:id",
  checkJwt,
  syncUser,
  requireStaff,
  validateProductId,
  validateProduct,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const updateData = req.body;

    try {
      const product = await Product.findByPk(id);

      if (!product) {
        throw new NotFoundError("Product");
      }

      // Validate category exists
      if (updateData.category_id) {
        const category = await Category.findByPk(updateData.category_id);
        if (!category) {
          return res.status(400).json({
            error: "Category not found",
          });
        }
      }

      // Check for duplicate slug (excluding current product)
      if (updateData.slug && updateData.slug !== product.slug) {
        const existingProduct = await Product.findOne({
          where: {
            slug: updateData.slug,
            id: { [require("sequelize").Op.ne]: id },
          },
        });
        if (existingProduct) {
          return res.status(409).json({
            error: "Product with this slug already exists",
          });
        }
      }

      // Check for duplicate SKU (excluding current product)
      if (updateData.sku && updateData.sku !== product.sku) {
        const existingProduct = await Product.findOne({
          where: {
            sku: updateData.sku,
            id: { [require("sequelize").Op.ne]: id },
          },
        });
        if (existingProduct) {
          return res.status(409).json({
            error: "Product with this SKU already exists",
          });
        }
      }

      // Validate discount price
      const finalPrice = updateData.price !== undefined ? updateData.price : product.price;
      const finalDiscountPrice = updateData.discount_price !== undefined ? updateData.discount_price : product.discount_price;

      if (finalDiscountPrice && finalPrice && finalDiscountPrice >= finalPrice) {
        return res.status(400).json({
          error: "Discount price must be less than regular price",
        });
      }

      const updatedProduct = await product.update(updateData);

      logger.businessEvent("product_updated", {
        productId: product.id,
        productName: product.name,
        updatedBy: req.user.id,
      });

      res.json({
        success: true,
        message: "Product updated successfully",
        product: updatedProduct.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Update product error:", error);
      throw error;
    }
  })
);

// DELETE /api/v1/products/:id - Delete product (Admin only)
router.delete(
  "/:id",
  checkJwt,
  syncUser,
  requireStaff,
  validateProductId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    try {
      const product = await Product.findByPk(id);

      if (!product) {
        throw new NotFoundError("Product");
      }

      // Soft delete - mark as inactive instead of hard delete
      await product.update({ is_active: false });

      logger.businessEvent("product_deleted", {
        productId: id,
        productName: product.name,
        deletedBy: req.user.id,
      });

      res.json({
        success: true,
        message: "Product deleted successfully",
      });
    } catch (error) {
      logger.error("Delete product error:", error);
      throw error;
    }
  })
);

// PATCH /api/v1/products/:id/toggle-status - Toggle product status (Admin only)
router.patch(
  "/:id/toggle-status",
  checkJwt,
  syncUser,
  requireStaff,
  validateProductId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    try {
      const product = await Product.findByPk(id);

      if (!product) {
        throw new NotFoundError("Product");
      }

      const updatedProduct = await product.update({
        is_active: !product.is_active,
      });

      logger.businessEvent("product_status_toggled", {
        productId: id,
        productName: product.name,
        newStatus: updatedProduct.is_active,
        updatedBy: req.user.id,
      });

      res.json({
        success: true,
        message: `Product ${updatedProduct.is_active ? "activated" : "deactivated"} successfully`,
        product: updatedProduct.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Toggle product status error:", error);
      throw error;
    }
  })
);

// PATCH /api/v1/products/:id/toggle-featured - Toggle featured status (Admin only)
router.patch(
  "/:id/toggle-featured",
  checkJwt,
  syncUser,
  requireStaff,
  validateProductId,
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;

    try {
      const product = await Product.findByPk(id);

      if (!product) {
        throw new NotFoundError("Product");
      }

      const updatedProduct = await product.update({
        is_featured: !product.is_featured,
      });

      logger.businessEvent("product_featured_toggled", {
        productId: id,
        productName: product.name,
        newFeaturedStatus: updatedProduct.is_featured,
        updatedBy: req.user.id,
      });

      res.json({
        success: true,
        message: `Product ${updatedProduct.is_featured ? "marked as featured" : "removed from featured"}`,
        product: updatedProduct.toSafeJSON(),
      });
    } catch (error) {
      logger.error("Toggle product featured error:", error);
      throw error;
    }
  })
);

// GET /api/v1/products/:id/related - Get related products
router.get(
  "/:id/related",
  productLimiter,
  optionalAuth,
  validateProductId,
  query("limit").optional().isInt({ min: 1, max: 20 }).withMessage("Limit must be between 1 and 20"),
  handleValidationError,
  asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { limit = 6 } = req.query;

    try {
      const product = await Product.findByPk(id);

      if (!product) {
        throw new NotFoundError("Product");
      }

      // Find related products by category, brand, or room type
      const { Op } = require("sequelize");
      const whereClause = {
        id: { [Op.ne]: id },
        is_active: true,
        [Op.or]: [],
      };

      if (product.category_id) {
        whereClause[Op.or].push({ category_id: product.category_id });
      }
      if (product.brand) {
        whereClause[Op.or].push({ brand: product.brand });
      }
      if (product.room_type) {
        whereClause[Op.or].push({ room_type: product.room_type });
      }

      // If no criteria, fall back to same category or random products
      if (whereClause[Op.or].length === 0) {
        delete whereClause[Op.or];
      }

      const relatedProducts = await Product.findAll({
        where: whereClause,
        include: [
          {
            model: Category,
            as: "category",
          },
          {
            model: ProductImage,
            as: "images",
            where: { is_primary: true },
            required: false,
          },
        ],
        limit: parseInt(limit),
        order: [["created_at", "DESC"]],
      });

      res.json({
        success: true,
        products: relatedProducts.map((p) => p.toSafeJSON()),
        count: relatedProducts.length,
      });
    } catch (error) {
      logger.error("Get related products error:", error);
      throw error;
    }
  })
);

module.exports = router;
