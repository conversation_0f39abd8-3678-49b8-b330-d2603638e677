const express = require('express');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

const { 
  checkJwt, 
  syncUser, 
  requireStaff, 
  auth0Helpers 
} = require('../middleware/auth');
const { User } = require('../models');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: { error: 'Too many authentication attempts, please try again later' }
});

// Validation middleware
const validateProfile = [
  body('first_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('First name must be between 1 and 100 characters'),
  body('last_name')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Last name must be less than 100 characters'),
  body('phone')
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Invalid phone number format'),
  body('timezone')
    .optional()
    .isIn(['Asia/Kolkata', 'UTC', 'America/New_York', 'Europe/London'])
    .withMessage('Invalid timezone'),
  body('language')
    .optional()
    .isIn(['en', 'hi', 'es', 'fr'])
    .withMessage('Invalid language code')
];

// POST /api/v1/auth/login - Handle login (called after Auth0 authentication)
router.post('/login', authLimiter, checkJwt, syncUser, async (req, res) => {
  try {
    const user = req.user;
    
    // Update last login timestamp
    await User.updateLastLogin(user.id);
    
    // Log successful login
    logger.info(`User logged in: ${user.email} (${user.user_type})`);
    
    res.json({
      success: true,
      message: 'Login successful',
      user: user.toSafeJSON(),
      permissions: {
        canAccessPortal: user.canAccessPortal(),
        userType: user.user_type
      }
    });
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      error: 'Internal server error during login'
    });
  }
});

// GET /api/v1/auth/me - Get current user profile
router.get('/me', checkJwt, syncUser, async (req, res) => {
  try {
    const user = req.user;
    
    res.json({
      success: true,
      user: user.toSafeJSON(),
      permissions: {
        canAccessPortal: user.canAccessPortal(),
        userType: user.user_type
      }
    });
  } catch (error) {
    logger.error('Get profile error:', error);
    res.status(500).json({
      error: 'Failed to fetch user profile'
    });
  }
});

// PUT /api/v1/auth/profile - Update user profile
router.put('/profile', checkJwt, syncUser, validateProfile, async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }
    
    const user = req.user;
    const { first_name, last_name, phone, timezone, language } = req.body;
    
    // Update local database
    const updatedUser = await user.update({
      first_name: first_name || user.first_name,
      last_name: last_name || user.last_name,
      phone: phone || user.phone,
      timezone: timezone || user.timezone,
      language: language || user.language
    });
    
    // Also update Auth0 user profile
    try {
      await auth0Helpers.updateUser(user.auth0_id, {
        given_name: first_name,
        family_name: last_name,
        user_metadata: {
          phone,
          timezone,
          language
        }
      });
    } catch (auth0Error) {
      logger.warn('Failed to update Auth0 profile:', auth0Error);
      // Continue anyway - local update succeeded
    }
    
    logger.info(`Profile updated for user: ${user.email}`);
    
    res.json({
      success: true,
      message: 'Profile updated successfully',
      user: updatedUser.toSafeJSON()
    });
  } catch (error) {
    logger.error('Profile update error:', error);
    res.status(500).json({
      error: 'Failed to update profile'
    });
  }
});

// POST /api/v1/auth/refresh - Refresh user session (verify token is still valid)
router.post('/refresh', checkJwt, syncUser, async (req, res) => {
  try {
    const user = req.user;
    
    // Check if user is still active
    if (!user.is_active) {
      return res.status(403).json({
        error: 'Account has been deactivated'
      });
    }
    
    res.json({
      success: true,
      message: 'Session refreshed',
      user: user.toSafeJSON(),
      permissions: {
        canAccessPortal: user.canAccessPortal(),
        userType: user.user_type
      }
    });
  } catch (error) {
    logger.error('Session refresh error:', error);
    res.status(500).json({
      error: 'Failed to refresh session'
    });
  }
});

// POST /api/v1/auth/logout - Handle logout
router.post('/logout', checkJwt, syncUser, async (req, res) => {
  try {
    const user = req.user;
    
    logger.info(`User logged out: ${user.email}`);
    
    res.json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      error: 'Failed to logout'
    });
  }
});

// DELETE /api/v1/auth/account - Delete user account
router.delete('/account', authLimiter, checkJwt, syncUser, async (req, res) => {
  try {
    const user = req.user;
    
    // Only customers can delete their own accounts
    if (!user.isCustomer()) {
      return res.status(403).json({
        error: 'Staff accounts cannot be deleted through this endpoint'
      });
    }
    
    // Soft delete - mark as inactive instead of hard delete
    await user.update({ is_active: false });
    
    // Block user in Auth0
    try {
      await auth0Helpers.updateUserStatus(user.auth0_id, true);
    } catch (auth0Error) {
      logger.warn('Failed to block user in Auth0:', auth0Error);
    }
    
    logger.info(`Account deleted for user: ${user.email}`);
    
    res.json({
      success: true,
      message: 'Account deleted successfully'
    });
  } catch (error) {
    logger.error('Account deletion error:', error);
    res.status(500).json({
      error: 'Failed to delete account'
    });
  }
});

// Admin routes for user management

// GET /api/v1/auth/users - Get all users (admin only)
router.get('/users', checkJwt, syncUser, requireStaff, async (req, res) => {
  try {
    const { page = 1, limit = 20, type, search } = req.query;
    const offset = (page - 1) * limit;
    
    const whereClause = {};
    
    // Filter by user type
    if (type && ['customer', 'admin', 'designer', 'sales'].includes(type)) {
      whereClause.user_type = type;
    }
    
    // Search by name or email
    if (search) {
      const { Op } = require('sequelize');
      whereClause[Op.or] = [
        { first_name: { [Op.iLike]: `%${search}%` } },
        { last_name: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    const { rows: users, count } = await User.findAndCountAll({
      where: whereClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']],
      attributes: { exclude: ['auth0_id'] }
    });
    
    res.json({
      success: true,
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    logger.error('Get users error:', error);
    res.status(500).json({
      error: 'Failed to fetch users'
    });
  }
});

// PUT /api/v1/auth/users/:id/status - Update user status (admin only)
router.put('/users/:id/status', checkJwt, syncUser, requireStaff, async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (typeof is_active !== 'boolean') {
      return res.status(400).json({
        error: 'is_active must be a boolean value'
      });
    }
    
    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        error: 'User not found'
      });
    }
    
    // Update local database
    await user.update({ is_active });
    
    // Update Auth0 status
    try {
      await auth0Helpers.updateUserStatus(user.auth0_id, !is_active);
    } catch (auth0Error) {
      logger.warn('Failed to update Auth0 user status:', auth0Error);
    }
    
    logger.info(`User status updated: ${user.email} - Active: ${is_active}`);
    
    res.json({
      success: true,
      message: `User ${is_active ? 'activated' : 'deactivated'} successfully`,
      user: user.toSafeJSON()
    });
  } catch (error) {
    logger.error('Update user status error:', error);
    res.status(500).json({
      error: 'Failed to update user status'
    });
  }
});

module.exports = router;