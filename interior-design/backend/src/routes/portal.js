const express = require("express");
const { query } = require("../database/connection");
const { checkJwt, syncUser, requireStaff } = require("../middleware/auth");
const { asyncHandler } = require("../middleware/errorHandler");

const router = express.Router();

// Dashboard stats
router.get(
  "/dashboard",
  checkJwt,
  syncUser,
  requireStaff,
  asyncHandler(async (req, res) => {
    // Get basic stats
    const stats = await Promise.all([
      query("SELECT COUNT(*) as count FROM projects WHERE status != $1", ["cancelled"]),
      query("SELECT COUNT(*) as count FROM users WHERE user_type = $1 AND is_active = true", ["customer"]),
      query("SELECT COUNT(*) as count FROM products WHERE status = $1", ["active"]),
      query("SELECT COUNT(*) as count FROM whatsapp_conversations WHERE status IN ($1, $2)", ["open", "in_progress"]),
    ]);

    res.json({
      success: true,
      data: {
        activeProjects: parseInt(stats[0].rows[0].count),
        totalCustomers: parseInt(stats[1].rows[0].count),
        activeProducts: parseInt(stats[2].rows[0].count),
        openChats: parseInt(stats[3].rows[0].count),
      },
    });
  })
);

module.exports = router;
