const express = require("express");
const { checkJwt, syncUser, requireStaff } = require("../middleware/auth");
const { asyncHandler } = require("../middleware/errorHandler");
const analyticsService = require("../services/analyticsService");

const router = express.Router();

// Dashboard stats
router.get(
  "/dashboard",
  checkJwt,
  syncUser,
  requireStaff,
  asyncHandler(async (req, res) => {
    try {
      const stats = await analyticsService.getDashboardStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      // Fallback to basic stats if analytics service fails
      const { User, Product, Project } = require("../models");

      const basicStats = await Promise.all([
        Project.count({ where: { status: { [require("sequelize").Op.ne]: "cancelled" } } }),
        User.count({ where: { user_type: "customer", is_active: true } }),
        Product.count({ where: { is_active: true } }),
      ]);

      res.json({
        success: true,
        data: {
          activeProjects: basicStats[0] || 0,
          totalCustomers: basicStats[1] || 0,
          activeProducts: basicStats[2] || 0,
          openChats: 0, // Placeholder
        },
      });
    }
  })
);

module.exports = router;
