const express = require('express');
const { query } = require('../database/connection');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Dashboard stats
router.get('/dashboard', authenticateToken, async (req, res) => {
  try {
    // Get basic stats
    const stats = await Promise.all([
      query('SELECT COUNT(*) as count FROM projects WHERE status != $1', ['cancelled']),
      query('SELECT COUNT(*) as count FROM customers WHERE is_active = true'),
      query('SELECT COUNT(*) as count FROM products WHERE status = $1', ['active']),
      query('SELECT COUNT(*) as count FROM whatsapp_conversations WHERE status IN ($1, $2)', ['open', 'in_progress'])
    ]);

    res.json({
      success: true,
      data: {
        activeProjects: parseInt(stats[0].rows[0].count),
        totalCustomers: parseInt(stats[1].rows[0].count),
        activeProducts: parseInt(stats[2].rows[0].count),
        openChats: parseInt(stats[3].rows[0].count)
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard data'
    });
  }
});

module.exports = router;