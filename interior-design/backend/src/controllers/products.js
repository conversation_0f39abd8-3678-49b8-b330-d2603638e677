const { validationResult } = require('express-validator');
const { Op } = require('sequelize');
const Product = require('../models/Product');
const Category = require('../models/Category');
const ProductImage = require('../models/ProductImage');
const User = require('../models/User');
const logger = require('../utils/logger');
const { uploadProductImages } = require('../services/s3Service');

class ProductController {
  // Get all products with filtering and pagination
  async getProducts(req, res) {
    try {
      const {
        page = 1,
        limit = 20,
        category,
        search,
        minPrice,
        maxPrice,
        status = 'active',
        sortBy = 'created_at',
        sortOrder = 'DESC',
        featured
      } = req.query;

      const offset = (page - 1) * limit;
      let whereClause = {};
      let categoryWhere = {};

      // Filter by status
      if (status) {
        whereClause.status = status;
      }

      // Filter by category
      if (category) {
        categoryWhere.slug = category;
      }

      // Search in name and description
      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } },
          { sku: { [Op.iLike]: `%${search}%` } }
        ];
      }

      // Price range filter
      if (minPrice || maxPrice) {
        whereClause.price = {};
        if (minPrice) whereClause.price[Op.gte] = parseFloat(minPrice);
        if (maxPrice) whereClause.price[Op.lte] = parseFloat(maxPrice);
      }

      // Featured filter
      if (featured !== undefined) {
        whereClause.is_featured = featured === 'true';
      }

      const products = await Product.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Category,
            as: 'category',
            where: Object.keys(categoryWhere).length > 0 ? categoryWhere : undefined,
            attributes: ['id', 'name', 'slug']
          },
          {
            model: ProductImage,
            as: 'images',
            attributes: ['id', 'image_url', 'alt_text', 'is_primary', 'sort_order']
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'first_name', 'last_name']
          }
        ],
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [[sortBy, sortOrder]],
        distinct: true
      });

      res.json({
        success: true,
        data: {
          products: products.rows,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: products.count,
            pages: Math.ceil(products.count / limit)
          }
        }
      });

    } catch (error) {
      logger.error('Get products error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch products'
      });
    }
  }

  // Get single product by ID or slug
  async getProduct(req, res) {
    try {
      const { id } = req.params;
      
      // Check if id is a UUID or slug
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
      const whereClause = isUUID ? { id } : { slug: id };

      const product = await Product.findOne({
        where: whereClause,
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name', 'slug', 'description']
          },
          {
            model: ProductImage,
            as: 'images',
            attributes: ['id', 'image_url', 'alt_text', 'is_primary', 'sort_order'],
            order: [['sort_order', 'ASC']]
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'first_name', 'last_name']
          }
        ]
      });

      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }

      // Increment view count
      await product.increment('view_count');

      res.json({
        success: true,
        data: { product }
      });

    } catch (error) {
      logger.error('Get product error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch product'
      });
    }
  }

  // Create new product (Admin/Staff only)
  async createProduct(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          errors: errors.array()
        });
      }

      const {
        name,
        description,
        price,
        comparePrice,
        sku,
        categoryId,
        tags,
        specifications,
        dimensions,
        materials,
        colors,
        isFeatured = false,
        status = 'active',
        metaTitle,
        metaDescription
      } = req.body;

      // Check if category exists
      const category = await Category.findByPk(categoryId);
      if (!category) {
        return res.status(400).json({
          success: false,
          message: 'Invalid category ID'
        });
      }

      // Check if SKU already exists
      if (sku) {
        const existingProduct = await Product.findOne({ where: { sku } });
        if (existingProduct) {
          return res.status(400).json({
            success: false,
            message: 'Product with this SKU already exists'
          });
        }
      }

      // Create product
      const product = await Product.create({
        name,
        description,
        price: parseFloat(price),
        compare_price: comparePrice ? parseFloat(comparePrice) : null,
        sku,
        category_id: categoryId,
        tags: tags ? JSON.parse(tags) : [],
        specifications: specifications ? JSON.parse(specifications) : {},
        dimensions: dimensions ? JSON.parse(dimensions) : {},
        materials: materials ? JSON.parse(materials) : [],
        colors: colors ? JSON.parse(colors) : [],
        is_featured: isFeatured,
        status,
        meta_title: metaTitle,
        meta_description: metaDescription,
        created_by: req.user.userId
      });

      // Handle image uploads if present
      if (req.files && req.files.length > 0) {
        try {
          const imageResults = await uploadProductImages(req.files, product.id);
          
          // Create ProductImage records
          const imagePromises = imageResults.map((result, index) => {
            return ProductImage.create({
              product_id: product.id,
              image_url: result.original.Location,
              thumbnail_url: result.thumbnail.Location,
              alt_text: `${product.name} - Image ${index + 1}`,
              is_primary: index === 0,
              sort_order: index
            });
          });

          await Promise.all(imagePromises);
        } catch (uploadError) {
          logger.error('Image upload error:', uploadError);
          // Continue without images, log the error
        }
      }

      // Fetch the created product with associations
      const createdProduct = await Product.findByPk(product.id, {
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name', 'slug']
          },
          {
            model: ProductImage,
            as: 'images',
            attributes: ['id', 'image_url', 'thumbnail_url', 'alt_text', 'is_primary', 'sort_order']
          }
        ]
      });

      res.status(201).json({
        success: true,
        message: 'Product created successfully',
        data: { product: createdProduct }
      });

    } catch (error) {
      logger.error('Create product error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create product'
      });
    }
  }

  // Update product (Admin/Staff only)
  async updateProduct(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const {
        name,
        description,
        price,
        comparePrice,
        sku,
        categoryId,
        tags,
        specifications,
        dimensions,
        materials,
        colors,
        isFeatured,
        status,
        metaTitle,
        metaDescription
      } = req.body;

      const product = await Product.findByPk(id);
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }

      // Check if category exists (if provided)
      if (categoryId) {
        const category = await Category.findByPk(categoryId);
        if (!category) {
          return res.status(400).json({
            success: false,
            message: 'Invalid category ID'
          });
        }
      }

      // Check if SKU already exists (if changed)
      if (sku && sku !== product.sku) {
        const existingProduct = await Product.findOne({ 
          where: { 
            sku,
            id: { [Op.ne]: id }
          } 
        });
        if (existingProduct) {
          return res.status(400).json({
            success: false,
            message: 'Product with this SKU already exists'
          });
        }
      }

      // Update product
      const updateData = {};
      if (name !== undefined) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (price !== undefined) updateData.price = parseFloat(price);
      if (comparePrice !== undefined) updateData.compare_price = comparePrice ? parseFloat(comparePrice) : null;
      if (sku !== undefined) updateData.sku = sku;
      if (categoryId !== undefined) updateData.category_id = categoryId;
      if (tags !== undefined) updateData.tags = typeof tags === 'string' ? JSON.parse(tags) : tags;
      if (specifications !== undefined) updateData.specifications = typeof specifications === 'string' ? JSON.parse(specifications) : specifications;
      if (dimensions !== undefined) updateData.dimensions = typeof dimensions === 'string' ? JSON.parse(dimensions) : dimensions;
      if (materials !== undefined) updateData.materials = typeof materials === 'string' ? JSON.parse(materials) : materials;
      if (colors !== undefined) updateData.colors = typeof colors === 'string' ? JSON.parse(colors) : colors;
      if (isFeatured !== undefined) updateData.is_featured = isFeatured;
      if (status !== undefined) updateData.status = status;
      if (metaTitle !== undefined) updateData.meta_title = metaTitle;
      if (metaDescription !== undefined) updateData.meta_description = metaDescription;

      await product.update(updateData);

      // Handle new image uploads if present
      if (req.files && req.files.length > 0) {
        try {
          const imageResults = await uploadProductImages(req.files, product.id);
          
          // Get current max sort order
          const maxSortOrder = await ProductImage.max('sort_order', {
            where: { product_id: product.id }
          }) || 0;

          // Create new ProductImage records
          const imagePromises = imageResults.map((result, index) => {
            return ProductImage.create({
              product_id: product.id,
              image_url: result.original.Location,
              thumbnail_url: result.thumbnail.Location,
              alt_text: `${product.name} - Image ${maxSortOrder + index + 1}`,
              is_primary: false,
              sort_order: maxSortOrder + index + 1
            });
          });

          await Promise.all(imagePromises);
        } catch (uploadError) {
          logger.error('Image upload error:', uploadError);
        }
      }

      // Fetch updated product with associations
      const updatedProduct = await Product.findByPk(id, {
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name', 'slug']
          },
          {
            model: ProductImage,
            as: 'images',
            attributes: ['id', 'image_url', 'thumbnail_url', 'alt_text', 'is_primary', 'sort_order'],
            order: [['sort_order', 'ASC']]
          }
        ]
      });

      res.json({
        success: true,
        message: 'Product updated successfully',
        data: { product: updatedProduct }
      });

    } catch (error) {
      logger.error('Update product error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update product'
      });
    }
  }

  // Delete product (Admin only)
  async deleteProduct(req, res) {
    try {
      const { id } = req.params;

      const product = await Product.findByPk(id);
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }

      // Soft delete - just update status
      await product.update({ status: 'deleted' });

      res.json({
        success: true,
        message: 'Product deleted successfully'
      });

    } catch (error) {
      logger.error('Delete product error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete product'
      });
    }
  }

  // Get featured products
  async getFeaturedProducts(req, res) {
    try {
      const { limit = 8 } = req.query;

      const products = await Product.findAll({
        where: {
          is_featured: true,
          status: 'active'
        },
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name', 'slug']
          },
          {
            model: ProductImage,
            as: 'images',
            attributes: ['id', 'image_url', 'thumbnail_url', 'alt_text', 'is_primary'],
            where: { is_primary: true },
            required: false
          }
        ],
        limit: parseInt(limit),
        order: [['created_at', 'DESC']]
      });

      res.json({
        success: true,
        data: { products }
      });

    } catch (error) {
      logger.error('Get featured products error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch featured products'
      });
    }
  }

  // Get related products
  async getRelatedProducts(req, res) {
    try {
      const { id } = req.params;
      const { limit = 4 } = req.query;

      const product = await Product.findByPk(id);
      if (!product) {
        return res.status(404).json({
          success: false,
          message: 'Product not found'
        });
      }

      const relatedProducts = await Product.findAll({
        where: {
          category_id: product.category_id,
          id: { [Op.ne]: id },
          status: 'active'
        },
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name', 'slug']
          },
          {
            model: ProductImage,
            as: 'images',
            attributes: ['id', 'image_url', 'thumbnail_url', 'alt_text', 'is_primary'],
            where: { is_primary: true },
            required: false
          }
        ],
        limit: parseInt(limit),
        order: [['view_count', 'DESC']]
      });

      res.json({
        success: true,
        data: { products: relatedProducts }
      });

    } catch (error) {
      logger.error('Get related products error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch related products'
      });
    }
  }
}

module.exports = new ProductController();
