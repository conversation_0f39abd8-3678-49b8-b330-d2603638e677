#!/usr/bin/env node

require('dotenv').config();
const { migrate, rollback, healthCheck } = require('./migration');
const { connectDB, closeDB } = require('./connection');
const logger = require('../utils/logger');

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];

const showHelp = () => {
  console.log(`
🏗️  Database Migration Tool

Usage: node migrate.js [command]

Commands:
  migrate     Run database migration (create tables, indexes, seed data)
  rollback    Drop all tables and extensions (⚠️  DESTRUCTIVE)
  health      Check database health and table status
  help        Show this help message

Examples:
  node src/database/migrate.js migrate
  node src/database/migrate.js health
  node src/database/migrate.js rollback

Environment Variables Required:
  DB_HOST, DB_PORT, DB_NAME, DB_USERNAME, DB_PASSWORD

⚠️  Warning: Always backup your database before running rollback!
`);
};

const runCommand = async (cmd) => {
  try {
    // Connect to database
    await connectDB();
    logger.info('📡 Connected to database');

    switch (cmd) {
      case 'migrate':
        logger.info('🚀 Starting migration...');
        await migrate();
        logger.info('✅ Migration completed successfully!');
        break;

      case 'rollback':
        // Ask for confirmation
        const readline = require('readline');
        const rl = readline.createInterface({
          input: process.stdin,
          output: process.stdout
        });

        const answer = await new Promise((resolve) => {
          rl.question('⚠️  Are you sure you want to DROP ALL TABLES? This cannot be undone! (yes/no): ', resolve);
        });

        rl.close();

        if (answer.toLowerCase() === 'yes') {
          logger.warn('🗑️  Starting rollback...');
          await rollback();
          logger.warn('✅ Rollback completed!');
        } else {
          logger.info('❌ Rollback cancelled');
        }
        break;

      case 'health':
        logger.info('🔍 Checking database health...');
        const health = await healthCheck();
        
        if (health.status === 'healthy') {
          logger.info('✅ Database is healthy!');
          console.log('📊 All required tables exist and are accessible');
        } else {
          logger.warn('⚠️  Database health issues detected');
          if (health.missingTables && health.missingTables.length > 0) {
            console.log('❌ Missing tables:', health.missingTables.join(', '));
            console.log('💡 Run migration to create missing tables');
          }
          if (health.error) {
            console.log('❌ Error:', health.error);
          }
        }
        break;

      case 'help':
      case '--help':
      case '-h':
        showHelp();
        break;

      default:
        console.log('❌ Unknown command:', cmd);
        showHelp();
        process.exit(1);
    }

  } catch (error) {
    logger.error('💥 Command failed:', error);
    console.error('\n❌ Migration failed with error:', error.message);
    process.exit(1);
  } finally {
    // Close database connection
    await closeDB();
    logger.info('📡 Database connection closed');
  }
};

// Main execution
const main = async () => {
  if (!command) {
    console.log('❌ No command provided');
    showHelp();
    process.exit(1);
  }

  // Validate environment variables
  const requiredEnvVars = ['DB_HOST', 'DB_NAME', 'DB_USERNAME', 'DB_PASSWORD'];
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingEnvVars.join(', '));
    console.error('💡 Make sure your .env file is configured properly');
    process.exit(1);
  }

  await runCommand(command);
};

// Handle process termination gracefully
process.on('SIGINT', async () => {
  logger.info('🛑 Process interrupted, closing database connection...');
  await closeDB();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('🛑 Process terminated, closing database connection...');
  await closeDB();
  process.exit(0);
});

// Run the main function
main().catch(async (error) => {
  logger.error('💥 Unexpected error:', error);
  await closeDB();
  process.exit(1);
});