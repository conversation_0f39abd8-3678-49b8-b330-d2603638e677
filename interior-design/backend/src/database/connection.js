const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME,
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  dialect: 'postgres',
  dialectOptions: {
    ssl: process.env.DB_SSL === 'true' ? {
      require: true,
      rejectUnauthorized: false // For AWS RDS
    } : false
  },
  logging: process.env.NODE_ENV === 'development' ? 
    (msg) => logger.debug(msg) : false,
  pool: {
    max: 20,
    min: 0,
    acquire: 60000,
    idle: 10000
  },
  define: {
    timestamps: true,
    underscored: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  }
};

// Create Sequelize instance
const sequelize = new Sequelize(dbConfig);

// Test database connection
const connectDB = async () => {
  try {
    await sequelize.authenticate();
    logger.info('✅ PostgreSQL connection established successfully');
    
    if (process.env.NODE_ENV === 'development') {
      // Sync database in development (be careful with this in production)
      await sequelize.sync({ alter: false });
      logger.info('📊 Database synchronized');
    }
    
    return true;
  } catch (error) {
    logger.error('❌ Unable to connect to PostgreSQL database:', error);
    throw error;
  }
};

// Close database connection
const closeDB = async () => {
  try {
    await sequelize.close();
    logger.info('Database connection closed');
  } catch (error) {
    logger.error('Error closing database connection:', error);
    throw error;
  }
};

// Execute raw SQL queries
const executeQuery = async (query, replacements = {}) => {
  try {
    const result = await sequelize.query(query, {
      replacements,
      type: Sequelize.QueryTypes.SELECT
    });
    return result;
  } catch (error) {
    logger.error('Error executing query:', error);
    throw error;
  }
};

// Database health check
const healthCheck = async () => {
  try {
    await sequelize.authenticate();
    return { status: 'healthy', timestamp: new Date() };
  } catch (error) {
    return { status: 'unhealthy', error: error.message, timestamp: new Date() };
  }
};

module.exports = {
  sequelize,
  connectDB,
  closeDB,
  executeQuery,
  healthCheck,
  Sequelize
};