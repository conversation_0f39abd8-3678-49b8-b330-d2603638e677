const { sequelize } = require('./connection');
const logger = require('../utils/logger');

// Migration script to create all tables
const createTables = async () => {
  try {
    logger.info('Starting database migration...');

    // Enable UUID extension
    await sequelize.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
    logger.info('✅ UUID extension enabled');

    // Create users table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        auth0_id VARCHAR(255) NOT NULL UNIQUE,
        email VARCHAR(255) NOT NULL UNIQUE,
        first_name VARCHAR(100) NOT NULL,
        last_name VA<PERSON><PERSON><PERSON>(100),
        phone VARCHAR(20),
        user_type VARCHAR(20) NOT NULL DEFAULT 'customer' CHECK (user_type IN ('customer', 'admin', 'designer', 'sales')),
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        avatar_url VARCHAR(500),
        timezone VARCHAR(50) DEFAULT 'Asia/Kolkata',
        language VARCHAR(10) DEFAULT 'en',
        last_login_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Users table created');

    // Create categories table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        parent_id UUID REFERENCES categories(id),
        image_url VARCHAR(500),
        sort_order INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        meta_title VARCHAR(255),
        meta_description VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Categories table created');

    // Create products table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS products (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        short_description VARCHAR(500),
        sku VARCHAR(100) UNIQUE,
        price DECIMAL(10,2),
        discount_price DECIMAL(10,2),
        category_id UUID REFERENCES categories(id),
        brand VARCHAR(100),
        material VARCHAR(100),
        dimensions VARCHAR(100),
        color VARCHAR(50),
        style VARCHAR(100),
        room_type VARCHAR(100),
        is_featured BOOLEAN DEFAULT false,
        is_active BOOLEAN DEFAULT true,
        stock_status VARCHAR(20) DEFAULT 'in_stock' CHECK (stock_status IN ('in_stock', 'out_of_stock', 'on_order')),
        weight DECIMAL(8,2),
        warranty_period VARCHAR(50),
        assembly_required BOOLEAN DEFAULT false,
        meta_title VARCHAR(255),
        meta_description VARCHAR(500),
        sort_order INTEGER DEFAULT 0,
        view_count INTEGER DEFAULT 0,
        created_by UUID REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Products table created');

    // Create product_images table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS product_images (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
        image_url VARCHAR(500) NOT NULL,
        alt_text VARCHAR(255),
        is_primary BOOLEAN DEFAULT false,
        sort_order INTEGER DEFAULT 0,
        file_name VARCHAR(255),
        file_size INTEGER,
        mime_type VARCHAR(100),
        width INTEGER,
        height INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Product images table created');

    // Create customer_profiles table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS customer_profiles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        whatsapp_number VARCHAR(20),
        address_line_1 VARCHAR(255),
        address_line_2 VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(100),
        pincode VARCHAR(10),
        property_type VARCHAR(50) CHECK (property_type IN ('apartment', 'house', 'villa', 'office', 'shop')),
        property_size VARCHAR(50),
        budget_range VARCHAR(50),
        preferred_style VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Customer profiles table created');

    // Create projects table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS projects (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        customer_id UUID REFERENCES users(id),
        designer_id UUID REFERENCES users(id),
        sales_person_id UUID REFERENCES users(id),
        project_name VARCHAR(255) NOT NULL,
        project_type VARCHAR(50) CHECK (project_type IN ('consultation', 'full_design', 'partial_design', 'product_only')),
        property_type VARCHAR(50),
        property_size VARCHAR(50),
        number_of_rooms INTEGER,
        budget_min DECIMAL(10,2),
        budget_max DECIMAL(10,2),
        preferred_style VARCHAR(100),
        timeline VARCHAR(50),
        status VARCHAR(50) DEFAULT 'inquiry' CHECK (status IN ('inquiry', 'consultation_scheduled', 'in_progress', 'design_ready', 'approved', 'execution', 'completed', 'cancelled')),
        priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
        notes TEXT,
        customer_notes TEXT,
        project_address TEXT,
        estimated_cost DECIMAL(12,2),
        final_cost DECIMAL(12,2),
        started_at TIMESTAMP,
        completed_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Projects table created');

    // Create project_files table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS project_files (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
        file_name VARCHAR(255) NOT NULL,
        file_url VARCHAR(500) NOT NULL,
        file_type VARCHAR(50) NOT NULL,
        file_category VARCHAR(50),
        uploaded_by UUID REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Project files table created');

    // Create whatsapp_conversations table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS whatsapp_conversations (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        customer_id UUID REFERENCES users(id),
        whatsapp_number VARCHAR(20) NOT NULL,
        customer_name VARCHAR(255),
        inquiry_type VARCHAR(50) DEFAULT 'general' CHECK (inquiry_type IN ('general', 'product', 'consultation', 'support')),
        status VARCHAR(50) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'closed')),
        assigned_to UUID REFERENCES users(id),
        project_id UUID REFERENCES projects(id),
        last_message_at TIMESTAMP,
        metadata JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ WhatsApp conversations table created');

    // Create whatsapp_messages table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS whatsapp_messages (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        conversation_id UUID REFERENCES whatsapp_conversations(id) ON DELETE CASCADE,
        whatsapp_message_id VARCHAR(255) UNIQUE,
        message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'document', 'audio', 'video', 'template')),
        message_content TEXT,
        media_url VARCHAR(500),
        media_caption TEXT,
        sender_type VARCHAR(20) CHECK (sender_type IN ('customer', 'staff')),
        sender_id UUID REFERENCES users(id),
        sender_name VARCHAR(255),
        is_read BOOLEAN DEFAULT false,
        delivery_status VARCHAR(20) DEFAULT 'sent' CHECK (delivery_status IN ('sent', 'delivered', 'read', 'failed')),
        error_message TEXT,
        template_name VARCHAR(255),
        template_params JSONB,
        metadata JSONB,
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ WhatsApp messages table created');

    // Create wishlists table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS wishlists (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        customer_id UUID REFERENCES users(id) ON DELETE CASCADE,
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(customer_id, product_id)
      );
    `);
    logger.info('✅ Wishlists table created');

    // Create product_reviews table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS product_reviews (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        product_id UUID REFERENCES products(id) ON DELETE CASCADE,
        customer_id UUID REFERENCES users(id) ON DELETE CASCADE,
        project_id UUID REFERENCES projects(id),
        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
        review_title VARCHAR(255),
        review_content TEXT,
        is_verified BOOLEAN DEFAULT false,
        is_approved BOOLEAN DEFAULT false,
        helpful_votes INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Product reviews table created');

    // Create leads table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS leads (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        customer_id UUID REFERENCES users(id),
        source VARCHAR(50) NOT NULL,
        medium VARCHAR(50),
        campaign VARCHAR(100),
        lead_score INTEGER DEFAULT 0,
        status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'qualified', 'proposal_sent', 'negotiation', 'won', 'lost')),
        assigned_to UUID REFERENCES users(id),
        converted_to_project UUID REFERENCES projects(id),
        notes TEXT,
        follow_up_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Leads table created');

    // Create activity_logs table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS activity_logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id),
        action VARCHAR(100) NOT NULL,
        entity_type VARCHAR(50),
        entity_id UUID,
        description TEXT,
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Activity logs table created');

    // Create settings table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS settings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type VARCHAR(50) DEFAULT 'string',
        description TEXT,
        is_public BOOLEAN DEFAULT false,
        updated_by UUID REFERENCES users(id),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    logger.info('✅ Settings table created');

    logger.info('🎉 All tables created successfully!');
  } catch (error) {
    logger.error('❌ Migration failed:', error);
    throw error;
  }
};

// Create indexes for better performance
const createIndexes = async () => {
  try {
    logger.info('Creating database indexes...');

    const indexes = [
      // Users indexes
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);',
      'CREATE INDEX IF NOT EXISTS idx_users_auth0_id ON users(auth0_id);',
      'CREATE INDEX IF NOT EXISTS idx_users_user_type ON users(user_type);',
      'CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);',
      
      // Categories indexes
      'CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug);',
      'CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);',
      'CREATE INDEX IF NOT EXISTS idx_categories_is_active ON categories(is_active);',
      'CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);',
      
      // Products indexes
      'CREATE INDEX IF NOT EXISTS idx_products_slug ON products(slug);',
      'CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);',
      'CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);',
      'CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active);',
      'CREATE INDEX IF NOT EXISTS idx_products_is_featured ON products(is_featured);',
      'CREATE INDEX IF NOT EXISTS idx_products_stock_status ON products(stock_status);',
      'CREATE INDEX IF NOT EXISTS idx_products_brand ON products(brand);',
      'CREATE INDEX IF NOT EXISTS idx_products_room_type ON products(room_type);',
      'CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);',
      'CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);',
      
      // Product images indexes
      'CREATE INDEX IF NOT EXISTS idx_product_images_product_id ON product_images(product_id);',
      'CREATE INDEX IF NOT EXISTS idx_product_images_is_primary ON product_images(is_primary);',
      'CREATE INDEX IF NOT EXISTS idx_product_images_sort_order ON product_images(sort_order);',
      
      // Projects indexes
      'CREATE INDEX IF NOT EXISTS idx_projects_customer_id ON projects(customer_id);',
      'CREATE INDEX IF NOT EXISTS idx_projects_designer_id ON projects(designer_id);',
      'CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);',
      'CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);',
      
      // WhatsApp indexes
      'CREATE INDEX IF NOT EXISTS idx_whatsapp_conversations_customer_id ON whatsapp_conversations(customer_id);',
      'CREATE INDEX IF NOT EXISTS idx_whatsapp_conversations_status ON whatsapp_conversations(status);',
      'CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_conversation_id ON whatsapp_messages(conversation_id);',
      'CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_sent_at ON whatsapp_messages(sent_at);',
      
      // Other indexes
      'CREATE INDEX IF NOT EXISTS idx_wishlists_customer_id ON wishlists(customer_id);',
      'CREATE INDEX IF NOT EXISTS idx_wishlists_product_id ON wishlists(product_id);',
      'CREATE INDEX IF NOT EXISTS idx_leads_status ON leads(status);',
      'CREATE INDEX IF NOT EXISTS idx_leads_created_at ON leads(created_at);',
      'CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at);',
      'CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(setting_key);'
    ];

    for (const indexQuery of indexes) {
      await sequelize.query(indexQuery);
    }

    logger.info('✅ All indexes created successfully!');
  } catch (error) {
    logger.error('❌ Index creation failed:', error);
    throw error;
  }
};

// Insert initial data
const seedInitialData = async () => {
  try {
    logger.info('Seeding initial data...');

    // Insert default categories
    await sequelize.query(`
      INSERT INTO categories (name, slug, description, sort_order) VALUES 
      ('Living Room', 'living-room', 'Furniture and decor for living rooms', 1),
      ('Bedroom', 'bedroom', 'Bedroom furniture and accessories', 2),
      ('Kitchen', 'kitchen', 'Kitchen furniture and appliances', 3),
      ('Dining Room', 'dining-room', 'Dining furniture and decor', 4),
      ('Office', 'office', 'Office furniture and workspace solutions', 5),
      ('Bathroom', 'bathroom', 'Bathroom fixtures and accessories', 6)
      ON CONFLICT (slug) DO NOTHING;
    `);
    logger.info('✅ Default categories inserted');

    // Insert default settings
    await sequelize.query(`
      INSERT INTO settings (setting_key, setting_value, description, is_public) VALUES
      ('site_name', 'InteriorCraft', 'Website name', true),
      ('contact_email', '<EMAIL>', 'Contact email', true),
      ('whatsapp_number', '+91XXXXXXXXXX', 'WhatsApp business number', true),
      ('currency', 'INR', 'Default currency', true),
      ('timezone', 'Asia/Kolkata', 'Default timezone', false),
      ('max_upload_size', '10485760', 'Maximum file upload size in bytes', false),
      ('items_per_page', '20', 'Default items per page for pagination', false)
      ON CONFLICT (setting_key) DO NOTHING;
    `);
    logger.info('✅ Default settings inserted');

    logger.info('🌱 Initial data seeded successfully!');
  } catch (error) {
    logger.error('❌ Seeding failed:', error);
    throw error;
  }
};

// Main migration function
const migrate = async () => {
  try {
    logger.info('🚀 Starting database migration process...');
    
    await createTables();
    await createIndexes();
    await seedInitialData();
    
    logger.info('✨ Database migration completed successfully!');
    logger.info('📊 Database is ready for use');
    
    return true;
  } catch (error) {
    logger.error('💥 Migration process failed:', error);
    throw error;
  }
};

// Rollback function (drop all tables)
const rollback = async () => {
  try {
    logger.warn('⚠️  Starting database rollback...');
    
    const tables = [
      'activity_logs',
      'settings',
      'leads',
      'product_reviews',
      'wishlists',
      'whatsapp_messages',
      'whatsapp_conversations',
      'project_files',
      'projects',
      'customer_profiles',
      'product_images',
      'products',
      'categories',
      'users'
    ];

    for (const table of tables) {
      await sequelize.query(`DROP TABLE IF EXISTS ${table} CASCADE;`);
      logger.info(`🗑️  Dropped table: ${table}`);
    }

    await sequelize.query('DROP EXTENSION IF EXISTS "uuid-ossp";');
    logger.info('🗑️  Dropped UUID extension');
    
    logger.warn('🧹 Database rollback completed');
    return true;
  } catch (error) {
    logger.error('❌ Rollback failed:', error);
    throw error;
  }
};

// Database health check
const healthCheck = async () => {
  try {
    await sequelize.authenticate();
    
    // Check if main tables exist
    const tables = ['users', 'categories', 'products', 'projects'];
    const results = await Promise.all(
      tables.map(async (table) => {
        try {
          await sequelize.query(`SELECT 1 FROM ${table} LIMIT 1;`);
          return { table, exists: true };
        } catch {
          return { table, exists: false };
        }
      })
    );
    
    const missingTables = results.filter(r => !r.exists).map(r => r.table);
    
    return {
      status: missingTables.length === 0 ? 'healthy' : 'unhealthy',
      missingTables,
      timestamp: new Date()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date()
    };
  }
};

module.exports = {
  migrate,
  rollback,
  createTables,
  createIndexes,
  seedInitialData,
  healthCheck
};