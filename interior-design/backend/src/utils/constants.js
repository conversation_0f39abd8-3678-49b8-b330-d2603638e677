// User types
const USER_TYPES = {
  CUSTOMER: 'customer',
  ADMIN: 'admin',
  DESIGNER: 'designer',
  SALES: 'sales'
};

// User statuses
const USER_STATUSES = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
  PENDING: 'pending'
};

// Product statuses
const PRODUCT_STATUSES = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  DRAFT: 'draft',
  DELETED: 'deleted'
};

// Project types
const PROJECT_TYPES = {
  CONSULTATION: 'consultation',
  FULL_DESIGN: 'full_design',
  PARTIAL_DESIGN: 'partial_design',
  PRODUCT_ONLY: 'product_only'
};

// Project statuses
const PROJECT_STATUSES = {
  INQUIRY: 'inquiry',
  CONSULTATION_SCHEDULED: 'consultation_scheduled',
  IN_PROGRESS: 'in_progress',
  DESIGN_READY: 'design_ready',
  APPROVED: 'approved',
  EXECUTION: 'execution',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  ON_HOLD: 'on_hold'
};

// Project priorities
const PROJECT_PRIORITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
};

// WhatsApp conversation statuses
const WHATSAPP_STATUSES = {
  OPEN: 'open',
  IN_PROGRESS: 'in_progress',
  RESOLVED: 'resolved',
  CLOSED: 'closed'
};

// WhatsApp message types
const WHATSAPP_MESSAGE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  DOCUMENT: 'document',
  TEMPLATE: 'template',
  INTERACTIVE: 'interactive'
};

// Category statuses
const CATEGORY_STATUSES = {
  ACTIVE: 'active',
  INACTIVE: 'inactive'
};

// File upload types
const UPLOAD_TYPES = {
  PRODUCT_IMAGE: 'product_image',
  CATEGORY_IMAGE: 'category_image',
  PROJECT_IMAGE: 'project_image',
  USER_AVATAR: 'user_avatar',
  DOCUMENT: 'document'
};

// Allowed file extensions
const ALLOWED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
const ALLOWED_DOCUMENT_EXTENSIONS = ['.pdf', '.doc', '.docx', '.txt'];

// File size limits (in bytes)
const FILE_SIZE_LIMITS = {
  IMAGE: 5 * 1024 * 1024, // 5MB
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  AVATAR: 2 * 1024 * 1024 // 2MB
};

// Image dimensions for resizing
const IMAGE_DIMENSIONS = {
  THUMBNAIL: { width: 300, height: 300 },
  MEDIUM: { width: 600, height: 600 },
  LARGE: { width: 1200, height: 1200 },
  BANNER: { width: 1920, height: 600 }
};

// Pagination defaults
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100
};

// Sort orders
const SORT_ORDERS = {
  ASC: 'ASC',
  DESC: 'DESC'
};

// Common sort fields
const SORT_FIELDS = {
  CREATED_AT: 'created_at',
  UPDATED_AT: 'updated_at',
  NAME: 'name',
  PRICE: 'price',
  VIEW_COUNT: 'view_count'
};

// Email templates
const EMAIL_TEMPLATES = {
  WELCOME: 'welcome',
  PASSWORD_RESET: 'password_reset',
  EMAIL_VERIFICATION: 'email_verification',
  CONSULTATION_REQUEST: 'consultation_request',
  PROJECT_UPDATE: 'project_update',
  ORDER_CONFIRMATION: 'order_confirmation'
};

// Notification types
const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error'
};

// API response messages
const RESPONSE_MESSAGES = {
  SUCCESS: 'Operation completed successfully',
  CREATED: 'Resource created successfully',
  UPDATED: 'Resource updated successfully',
  DELETED: 'Resource deleted successfully',
  NOT_FOUND: 'Resource not found',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  VALIDATION_ERROR: 'Validation error',
  INTERNAL_ERROR: 'Internal server error',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded'
};

// HTTP status codes
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500
};

// JWT token expiry times
const TOKEN_EXPIRY = {
  ACCESS_TOKEN: '7d',
  REFRESH_TOKEN: '30d',
  PASSWORD_RESET: '1h',
  EMAIL_VERIFICATION: '24h'
};

// Cache TTL (Time To Live) in seconds
const CACHE_TTL = {
  SHORT: 300, // 5 minutes
  MEDIUM: 1800, // 30 minutes
  LONG: 3600, // 1 hour
  VERY_LONG: 86400 // 24 hours
};

// Rate limiting windows (in milliseconds)
const RATE_LIMIT_WINDOWS = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000
};

// Database constraints
const DB_CONSTRAINTS = {
  EMAIL_MAX_LENGTH: 255,
  NAME_MAX_LENGTH: 100,
  PHONE_MAX_LENGTH: 20,
  ADDRESS_MAX_LENGTH: 500,
  DESCRIPTION_MAX_LENGTH: 2000,
  SKU_MAX_LENGTH: 50,
  SLUG_MAX_LENGTH: 100
};

// Regex patterns
const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_INDIAN: /^[6-9]\d{9}$/,
  PINCODE_INDIAN: /^[1-9][0-9]{5}$/,
  SKU: /^[A-Z0-9-_]+$/,
  SLUG: /^[a-z0-9-]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
};

// Currency settings
const CURRENCY = {
  CODE: 'INR',
  SYMBOL: '₹',
  DECIMAL_PLACES: 2
};

// Date formats
const DATE_FORMATS = {
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  DATE_ONLY: 'YYYY-MM-DD',
  DISPLAY: 'DD/MM/YYYY',
  DISPLAY_WITH_TIME: 'DD/MM/YYYY HH:mm'
};

// WhatsApp Business API settings
const WHATSAPP_CONFIG = {
  API_VERSION: 'v17.0',
  MESSAGE_TYPES: {
    TEXT: 'text',
    TEMPLATE: 'template',
    INTERACTIVE: 'interactive',
    MEDIA: 'media'
  },
  TEMPLATE_CATEGORIES: {
    MARKETING: 'MARKETING',
    UTILITY: 'UTILITY',
    AUTHENTICATION: 'AUTHENTICATION'
  }
};

// AWS S3 settings
const S3_CONFIG = {
  BUCKET_FOLDERS: {
    PRODUCTS: 'products',
    CATEGORIES: 'categories',
    PROJECTS: 'projects',
    USERS: 'users',
    DOCUMENTS: 'documents'
  },
  ACL: {
    PUBLIC_READ: 'public-read',
    PRIVATE: 'private'
  }
};

// Analytics time ranges
const ANALYTICS_RANGES = {
  LAST_7_DAYS: 7,
  LAST_30_DAYS: 30,
  LAST_90_DAYS: 90,
  LAST_YEAR: 365
};

// Environment types
const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production'
};

// Log levels
const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
};

// Export all constants
module.exports = {
  USER_TYPES,
  USER_STATUSES,
  PRODUCT_STATUSES,
  PROJECT_TYPES,
  PROJECT_STATUSES,
  PROJECT_PRIORITIES,
  WHATSAPP_STATUSES,
  WHATSAPP_MESSAGE_TYPES,
  CATEGORY_STATUSES,
  UPLOAD_TYPES,
  ALLOWED_IMAGE_EXTENSIONS,
  ALLOWED_DOCUMENT_EXTENSIONS,
  FILE_SIZE_LIMITS,
  IMAGE_DIMENSIONS,
  PAGINATION,
  SORT_ORDERS,
  SORT_FIELDS,
  EMAIL_TEMPLATES,
  NOTIFICATION_TYPES,
  RESPONSE_MESSAGES,
  HTTP_STATUS,
  TOKEN_EXPIRY,
  CACHE_TTL,
  RATE_LIMIT_WINDOWS,
  DB_CONSTRAINTS,
  REGEX_PATTERNS,
  CURRENCY,
  DATE_FORMATS,
  WHATSAPP_CONFIG,
  S3_CONFIG,
  ANALYTICS_RANGES,
  ENVIRONMENTS,
  LOG_LEVELS
};
