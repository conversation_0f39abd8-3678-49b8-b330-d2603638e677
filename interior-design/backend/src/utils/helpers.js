const crypto = require('crypto');
const slugify = require('slugify');
const { CURRENCY, DATE_FORMATS, REGEX_PATTERNS } = require('./constants');

/**
 * Generate a unique slug from a string
 * @param {string} text - Text to convert to slug
 * @param {string} suffix - Optional suffix to add
 * @returns {string} - Generated slug
 */
const generateSlug = (text, suffix = '') => {
  const baseSlug = slugify(text, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g
  });
  
  if (suffix) {
    return `${baseSlug}-${suffix}`;
  }
  
  return baseSlug;
};

/**
 * Generate a unique SKU
 * @param {string} prefix - SKU prefix
 * @param {number} length - Length of random part
 * @returns {string} - Generated SKU
 */
const generateSKU = (prefix = 'PRD', length = 8) => {
  const randomPart = crypto.randomBytes(length / 2).toString('hex').toUpperCase();
  return `${prefix}-${randomPart}`;
};

/**
 * Generate a random string
 * @param {number} length - Length of the string
 * @param {string} charset - Character set to use
 * @returns {string} - Random string
 */
const generateRandomString = (length = 10, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') => {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
};

/**
 * Format currency amount
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @returns {string} - Formatted currency string
 */
const formatCurrency = (amount, currency = CURRENCY.CODE) => {
  if (typeof amount !== 'number' || isNaN(amount)) {
    return `${CURRENCY.SYMBOL}0.00`;
  }
  
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: CURRENCY.DECIMAL_PLACES,
    maximumFractionDigits: CURRENCY.DECIMAL_PLACES
  }).format(amount);
};

/**
 * Format date
 * @param {Date|string} date - Date to format
 * @param {string} format - Format type
 * @returns {string} - Formatted date string
 */
const formatDate = (date, format = 'DISPLAY') => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  switch (format) {
    case 'ISO':
      return dateObj.toISOString();
    case 'DATE_ONLY':
      return dateObj.toISOString().split('T')[0];
    case 'DISPLAY':
      return dateObj.toLocaleDateString('en-IN');
    case 'DISPLAY_WITH_TIME':
      return dateObj.toLocaleString('en-IN');
    default:
      return dateObj.toLocaleDateString('en-IN');
  }
};

/**
 * Calculate time difference in human readable format
 * @param {Date|string} date - Date to compare
 * @returns {string} - Human readable time difference
 */
const timeAgo = (date) => {
  if (!date) return '';
  
  const now = new Date();
  const dateObj = new Date(date);
  const diffInSeconds = Math.floor((now - dateObj) / 1000);
  
  if (diffInSeconds < 60) return 'just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
  return `${Math.floor(diffInSeconds / 31536000)} years ago`;
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} - Is valid email
 */
const isValidEmail = (email) => {
  return REGEX_PATTERNS.EMAIL.test(email);
};

/**
 * Validate Indian phone number
 * @param {string} phone - Phone number to validate
 * @returns {boolean} - Is valid phone number
 */
const isValidIndianPhone = (phone) => {
  const cleanPhone = phone.replace(/\D/g, '');
  return REGEX_PATTERNS.PHONE_INDIAN.test(cleanPhone);
};

/**
 * Validate Indian pincode
 * @param {string} pincode - Pincode to validate
 * @returns {boolean} - Is valid pincode
 */
const isValidIndianPincode = (pincode) => {
  return REGEX_PATTERNS.PINCODE_INDIAN.test(pincode);
};

/**
 * Sanitize HTML content
 * @param {string} html - HTML content to sanitize
 * @returns {string} - Sanitized HTML
 */
const sanitizeHtml = (html) => {
  if (!html) return '';
  
  // Basic HTML sanitization - remove script tags and dangerous attributes
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/vbscript:/gi, '');
};

/**
 * Truncate text to specified length
 * @param {string} text - Text to truncate
 * @param {number} length - Maximum length
 * @param {string} suffix - Suffix to add when truncated
 * @returns {string} - Truncated text
 */
const truncateText = (text, length = 100, suffix = '...') => {
  if (!text || text.length <= length) return text || '';
  return text.substring(0, length).trim() + suffix;
};

/**
 * Extract text from HTML
 * @param {string} html - HTML content
 * @returns {string} - Plain text
 */
const extractTextFromHtml = (html) => {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '').trim();
};

/**
 * Generate pagination metadata
 * @param {number} page - Current page
 * @param {number} limit - Items per page
 * @param {number} total - Total items
 * @returns {object} - Pagination metadata
 */
const generatePagination = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;
  
  return {
    page: parseInt(page),
    limit: parseInt(limit),
    total: parseInt(total),
    pages: totalPages,
    hasNext,
    hasPrev,
    nextPage: hasNext ? page + 1 : null,
    prevPage: hasPrev ? page - 1 : null
  };
};

/**
 * Deep clone an object
 * @param {object} obj - Object to clone
 * @returns {object} - Cloned object
 */
const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

/**
 * Remove empty values from object
 * @param {object} obj - Object to clean
 * @returns {object} - Cleaned object
 */
const removeEmptyValues = (obj) => {
  const cleaned = {};
  for (const key in obj) {
    if (obj[key] !== null && obj[key] !== undefined && obj[key] !== '') {
      cleaned[key] = obj[key];
    }
  }
  return cleaned;
};

/**
 * Convert string to title case
 * @param {string} str - String to convert
 * @returns {string} - Title case string
 */
const toTitleCase = (str) => {
  if (!str) return '';
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

/**
 * Generate a hash from string
 * @param {string} str - String to hash
 * @param {string} algorithm - Hash algorithm
 * @returns {string} - Generated hash
 */
const generateHash = (str, algorithm = 'sha256') => {
  return crypto.createHash(algorithm).update(str).digest('hex');
};

/**
 * Check if value is empty
 * @param {any} value - Value to check
 * @returns {boolean} - Is empty
 */
const isEmpty = (value) => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

/**
 * Debounce function
 * @param {function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {function} - Debounced function
 */
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Throttle function
 * @param {function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {function} - Throttled function
 */
const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Calculate percentage
 * @param {number} value - Value
 * @param {number} total - Total
 * @param {number} decimals - Decimal places
 * @returns {number} - Percentage
 */
const calculatePercentage = (value, total, decimals = 2) => {
  if (total === 0) return 0;
  return parseFloat(((value / total) * 100).toFixed(decimals));
};

/**
 * Generate random color
 * @returns {string} - Random hex color
 */
const generateRandomColor = () => {
  return '#' + Math.floor(Math.random() * 16777215).toString(16);
};

/**
 * Convert bytes to human readable format
 * @param {number} bytes - Bytes to convert
 * @param {number} decimals - Decimal places
 * @returns {string} - Human readable size
 */
const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Mask sensitive data
 * @param {string} str - String to mask
 * @param {number} visibleChars - Number of visible characters at start and end
 * @returns {string} - Masked string
 */
const maskSensitiveData = (str, visibleChars = 2) => {
  if (!str || str.length <= visibleChars * 2) return str;
  
  const start = str.substring(0, visibleChars);
  const end = str.substring(str.length - visibleChars);
  const middle = '*'.repeat(str.length - visibleChars * 2);
  
  return start + middle + end;
};

module.exports = {
  generateSlug,
  generateSKU,
  generateRandomString,
  formatCurrency,
  formatDate,
  timeAgo,
  isValidEmail,
  isValidIndianPhone,
  isValidIndianPincode,
  sanitizeHtml,
  truncateText,
  extractTextFromHtml,
  generatePagination,
  deepClone,
  removeEmptyValues,
  toTitleCase,
  generateHash,
  isEmpty,
  debounce,
  throttle,
  calculatePercentage,
  generateRandomColor,
  formatBytes,
  maskSensitiveData
};
