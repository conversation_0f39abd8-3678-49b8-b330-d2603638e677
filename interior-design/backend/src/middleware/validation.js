const { body, param, query } = require('express-validator');

// User validation rules
const userValidation = {
  register: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    body('firstName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters')
      .matches(/^[a-zA-Z\s]+$/)
      .withMessage('First name can only contain letters and spaces'),
    body('lastName')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters')
      .matches(/^[a-zA-Z\s]+$/)
      .withMessage('Last name can only contain letters and spaces'),
    body('phone')
      .optional()
      .isMobilePhone('en-IN')
      .withMessage('Please provide a valid Indian mobile number'),
    body('userType')
      .optional()
      .isIn(['customer', 'admin', 'designer', 'sales'])
      .withMessage('Invalid user type')
  ],

  login: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address'),
    body('password')
      .notEmpty()
      .withMessage('Password is required')
  ],

  updateProfile: [
    body('firstName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('First name must be between 2 and 50 characters')
      .matches(/^[a-zA-Z\s]+$/)
      .withMessage('First name can only contain letters and spaces'),
    body('lastName')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('Last name must be between 2 and 50 characters')
      .matches(/^[a-zA-Z\s]+$/)
      .withMessage('Last name can only contain letters and spaces'),
    body('phone')
      .optional()
      .isMobilePhone('en-IN')
      .withMessage('Please provide a valid Indian mobile number'),
    body('address')
      .optional()
      .trim()
      .isLength({ max: 200 })
      .withMessage('Address must be less than 200 characters'),
    body('city')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('City must be between 2 and 50 characters'),
    body('state')
      .optional()
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('State must be between 2 and 50 characters'),
    body('pincode')
      .optional()
      .matches(/^[1-9][0-9]{5}$/)
      .withMessage('Please provide a valid Indian pincode')
  ],

  changePassword: [
    body('currentPassword')
      .notEmpty()
      .withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('New password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
  ],

  forgotPassword: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address')
  ],

  resetPassword: [
    body('token')
      .notEmpty()
      .withMessage('Reset token is required'),
    body('newPassword')
      .isLength({ min: 8 })
      .withMessage('New password must be at least 8 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
  ]
};

// Product validation rules
const productValidation = {
  create: [
    body('name')
      .trim()
      .isLength({ min: 3, max: 200 })
      .withMessage('Product name must be between 3 and 200 characters'),
    body('description')
      .trim()
      .isLength({ min: 10, max: 2000 })
      .withMessage('Product description must be between 10 and 2000 characters'),
    body('price')
      .isFloat({ min: 0 })
      .withMessage('Price must be a positive number'),
    body('comparePrice')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Compare price must be a positive number'),
    body('sku')
      .optional()
      .trim()
      .isLength({ min: 3, max: 50 })
      .withMessage('SKU must be between 3 and 50 characters')
      .matches(/^[A-Z0-9-_]+$/)
      .withMessage('SKU can only contain uppercase letters, numbers, hyphens, and underscores'),
    body('categoryId')
      .isUUID()
      .withMessage('Category ID must be a valid UUID'),
    body('status')
      .optional()
      .isIn(['active', 'inactive', 'draft'])
      .withMessage('Status must be active, inactive, or draft'),
    body('metaTitle')
      .optional()
      .trim()
      .isLength({ max: 60 })
      .withMessage('Meta title must be less than 60 characters'),
    body('metaDescription')
      .optional()
      .trim()
      .isLength({ max: 160 })
      .withMessage('Meta description must be less than 160 characters')
  ],

  update: [
    param('id')
      .isUUID()
      .withMessage('Product ID must be a valid UUID'),
    body('name')
      .optional()
      .trim()
      .isLength({ min: 3, max: 200 })
      .withMessage('Product name must be between 3 and 200 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ min: 10, max: 2000 })
      .withMessage('Product description must be between 10 and 2000 characters'),
    body('price')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Price must be a positive number'),
    body('comparePrice')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Compare price must be a positive number'),
    body('sku')
      .optional()
      .trim()
      .isLength({ min: 3, max: 50 })
      .withMessage('SKU must be between 3 and 50 characters')
      .matches(/^[A-Z0-9-_]+$/)
      .withMessage('SKU can only contain uppercase letters, numbers, hyphens, and underscores'),
    body('categoryId')
      .optional()
      .isUUID()
      .withMessage('Category ID must be a valid UUID'),
    body('status')
      .optional()
      .isIn(['active', 'inactive', 'draft'])
      .withMessage('Status must be active, inactive, or draft')
  ],

  getProducts: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('minPrice')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Minimum price must be a positive number'),
    query('maxPrice')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Maximum price must be a positive number'),
    query('status')
      .optional()
      .isIn(['active', 'inactive', 'draft'])
      .withMessage('Status must be active, inactive, or draft'),
    query('sortBy')
      .optional()
      .isIn(['name', 'price', 'created_at', 'view_count'])
      .withMessage('Sort by must be name, price, created_at, or view_count'),
    query('sortOrder')
      .optional()
      .isIn(['ASC', 'DESC'])
      .withMessage('Sort order must be ASC or DESC')
  ]
};

// Category validation rules
const categoryValidation = {
  create: [
    body('name')
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Category name must be between 2 and 100 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Category description must be less than 500 characters'),
    body('parentId')
      .optional()
      .isUUID()
      .withMessage('Parent ID must be a valid UUID'),
    body('metaTitle')
      .optional()
      .trim()
      .isLength({ max: 60 })
      .withMessage('Meta title must be less than 60 characters'),
    body('metaDescription')
      .optional()
      .trim()
      .isLength({ max: 160 })
      .withMessage('Meta description must be less than 160 characters')
  ],

  update: [
    param('id')
      .isUUID()
      .withMessage('Category ID must be a valid UUID'),
    body('name')
      .optional()
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Category name must be between 2 and 100 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Category description must be less than 500 characters'),
    body('parentId')
      .optional()
      .isUUID()
      .withMessage('Parent ID must be a valid UUID')
  ]
};

// Project validation rules
const projectValidation = {
  create: [
    body('projectName')
      .trim()
      .isLength({ min: 3, max: 200 })
      .withMessage('Project name must be between 3 and 200 characters'),
    body('projectType')
      .isIn(['consultation', 'full_design', 'partial_design', 'product_only'])
      .withMessage('Invalid project type'),
    body('propertyType')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('Property type must be less than 50 characters'),
    body('budgetMin')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Minimum budget must be a positive number'),
    body('budgetMax')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Maximum budget must be a positive number'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 2000 })
      .withMessage('Description must be less than 2000 characters'),
    body('timeline')
      .optional()
      .trim()
      .isLength({ max: 50 })
      .withMessage('Timeline must be less than 50 characters')
  ],

  update: [
    param('id')
      .isUUID()
      .withMessage('Project ID must be a valid UUID'),
    body('projectName')
      .optional()
      .trim()
      .isLength({ min: 3, max: 200 })
      .withMessage('Project name must be between 3 and 200 characters'),
    body('status')
      .optional()
      .isIn(['inquiry', 'consultation_scheduled', 'in_progress', 'design_ready', 'approved', 'execution', 'completed', 'cancelled'])
      .withMessage('Invalid project status'),
    body('priority')
      .optional()
      .isIn(['low', 'medium', 'high', 'urgent'])
      .withMessage('Invalid project priority')
  ]
};

// WhatsApp validation rules
const whatsappValidation = {
  sendMessage: [
    body('to')
      .isMobilePhone('en-IN')
      .withMessage('Please provide a valid Indian mobile number'),
    body('message')
      .trim()
      .isLength({ min: 1, max: 1000 })
      .withMessage('Message must be between 1 and 1000 characters'),
    body('type')
      .optional()
      .isIn(['text', 'template', 'media'])
      .withMessage('Message type must be text, template, or media')
  ]
};

// Common parameter validations
const commonValidation = {
  uuidParam: [
    param('id')
      .isUUID()
      .withMessage('ID must be a valid UUID')
  ],

  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ]
};

module.exports = {
  userValidation,
  productValidation,
  categoryValidation,
  projectValidation,
  whatsappValidation,
  commonValidation
};
