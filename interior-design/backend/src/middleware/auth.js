const { expressjwt: jwt } = require("express-jwt");
const jwksRsa = require("jwks-rsa");
const { ManagementClient } = require("auth0");
const axios = require("axios");
const logger = require("../utils/logger");
const { User } = require("../models");

// Check if Auth0 is properly configured
const isAuth0Configured = () => {
  return process.env.AUTH0_DOMAIN && process.env.AUTH0_AUDIENCE && process.env.AUTH0_CLIENT_ID && process.env.AUTH0_CLIENT_SECRET;
};

// Development mode bypass for testing
const isDevelopmentBypass = () => {
  return process.env.NODE_ENV === "development" && process.env.BYPASS_AUTH === "true";
};

// Auth0 JWT verification middleware
const checkJwt = (req, res, next) => {
  // In development, allow bypass if configured
  if (isDevelopmentBypass()) {
    logger.warn("⚠️  Authentication bypassed in development mode");
    // Create a mock user for development
    req.auth = {
      sub: "dev-user-123",
      email: "<EMAIL>",
    };
    return next();
  }

  // Check if Auth0 is configured
  if (!isAuth0Configured()) {
    logger.error("❌ Auth0 configuration missing");
    return res.status(500).json({
      error: "Authentication service not configured",
      message: "Please configure Auth0 environment variables",
    });
  }

  // Extract token from Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ error: "No token provided" });
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix

  // Check if token is encrypted (Auth0 opaque token) or JWT
  if (token.startsWith("eyJhbGciOiJkaXIi")) {
    // This is an encrypted token, use Auth0 userinfo endpoint
    return verifyEncryptedToken(token, req, res, next);
  }

  // Create JWT middleware for regular JWTs
  const jwtMiddleware = jwt({
    secret: jwksRsa.expressJwtSecret({
      cache: true,
      rateLimit: true,
      jwksRequestsPerMinute: 5,
      jwksUri: `https://${process.env.AUTH0_DOMAIN}/.well-known/jwks.json`,
    }),
    audience: process.env.AUTH0_AUDIENCE,
    issuer: `https://${process.env.AUTH0_DOMAIN}/`,
    algorithms: ["RS256"],
    credentialsRequired: true,
  });

  // Execute JWT middleware
  jwtMiddleware(req, res, next);
};

// Simple in-memory cache for token verification (to avoid rate limits)
const tokenCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Verify encrypted/opaque tokens using Auth0 userinfo endpoint
const verifyEncryptedToken = async (token, req, res, next) => {
  try {
    // Check cache first
    const cacheKey = token.substring(0, 50); // Use first 50 chars as cache key
    const cached = tokenCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      req.auth = cached.userInfo;
      return next();
    }

    // Call Auth0 userinfo endpoint to validate the token
    const response = await axios.get(`https://${process.env.AUTH0_DOMAIN}/userinfo`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      timeout: 5000, // 5 second timeout
    });

    // Extract user info from Auth0 response
    const userInfo = response.data;

    // Set req.auth to match the format expected by syncUser
    req.auth = {
      sub: userInfo.sub,
      email: userInfo.email,
      email_verified: userInfo.email_verified,
      name: userInfo.name,
      given_name: userInfo.given_name,
      family_name: userInfo.family_name,
      nickname: userInfo.nickname,
    };

    // Cache the result
    tokenCache.set(cacheKey, {
      userInfo: req.auth,
      timestamp: Date.now(),
    });

    // Clean up old cache entries periodically
    if (tokenCache.size > 100) {
      const now = Date.now();
      for (const [key, value] of tokenCache.entries()) {
        if (now - value.timestamp > CACHE_TTL) {
          tokenCache.delete(key);
        }
      }
    }

    next();
  } catch (error) {
    logger.error("Error verifying encrypted token:", error.response?.data || error.message);

    // Handle rate limiting specifically
    if (error.response?.status === 429 || error.response?.data?.error === "access_denied") {
      return res.status(429).json({
        error: "Rate limit exceeded",
        message: "Too many authentication requests. Please try again later.",
      });
    }

    return res.status(401).json({
      error: "Invalid token",
      message: "Token verification failed",
    });
  }
};

// Auth0 Management Client for user operations
let managementClient = null;

// Note: Management Client disabled for now since we're using userinfo endpoint
// To enable, you need Machine-to-Machine application credentials, not SPA credentials
// if (isAuth0Configured()) {
//   try {
//     managementClient = new ManagementClient({
//       domain: process.env.AUTH0_DOMAIN,
//       clientId: process.env.AUTH0_M2M_CLIENT_ID, // Need M2M client ID
//       clientSecret: process.env.AUTH0_M2M_CLIENT_SECRET, // Need M2M client secret
//       audience: process.env.AUTH0_MANAGEMENT_AUDIENCE,
//       scope: "read:users update:users create:users delete:users",
//     });
//   } catch (error) {
//     logger.error("Failed to initialize Auth0 Management Client:", error);
//   }
// }

// Middleware to sync Auth0 user with local database
const syncUser = async (req, res, next) => {
  try {
    if (!req.auth || !req.auth.sub) {
      return res.status(401).json({ error: "No user information found" });
    }

    const auth0Id = req.auth.sub;

    // Check if user exists in local database
    let user = await User.findOne({ where: { auth0_id: auth0Id } });

    if (!user) {
      // In development mode with bypass, create a default user
      if (isDevelopmentBypass()) {
        user = await User.findOrCreate({
          where: { auth0_id: auth0Id },
          defaults: {
            auth0_id: auth0Id,
            email: req.auth.email || "<EMAIL>",
            first_name: "Development",
            last_name: "User",
            user_type: "admin", // Give admin access in dev mode
            email_verified: true,
            is_active: true,
          },
        });
        user = user[0]; // findOrCreate returns [instance, created]
      } else {
        // Create user from the token info we already have (from userinfo endpoint)
        // This avoids additional API calls and rate limits
        user = await User.create({
          auth0_id: auth0Id,
          email: req.auth.email || "<EMAIL>",
          first_name: req.auth.given_name || req.auth.name || req.auth.nickname || "User",
          last_name: req.auth.family_name || "",
          user_type: determineUserType(req.auth, req),
          email_verified: req.auth.email_verified || false,
          is_active: true,
        });
      }

      logger.info(`New user created: ${user.email}`);
    } else {
      // Update last login time
      await user.update({ updated_at: new Date() });
    }

    // Attach user to request object
    req.user = user;
    req.auth0User = req.auth;

    next();
  } catch (error) {
    logger.error("Error syncing user:", error);
    return res.status(500).json({
      error: "Internal server error during user authentication",
    });
  }
};

// Determine user type based on Auth0 user metadata or roles
const determineUserType = (auth0User, req = null) => {
  // Check for roles in Auth0 user metadata
  const roles = auth0User.app_metadata?.roles || [];
  const userType = auth0User.app_metadata?.user_type;

  if (userType) {
    return userType;
  }

  if (roles.includes("admin")) return "admin";
  if (roles.includes("designer")) return "designer";
  if (roles.includes("sales")) return "sales";

  // Determine default based on the app being used
  if (req) {
    const origin = req.headers.origin || req.headers.referer || "";
    const userAgent = req.headers["user-agent"] || "";

    // Check if request is coming from portal app
    if (origin.includes(":3001") || origin.includes("portal") || userAgent.includes("Portal") || req.headers["x-app-type"] === "portal") {
      // Default to admin for portal app users
      return "admin";
    }

    // Also check for port 3000 with portal header (current setup)
    if (origin.includes(":3000") && req.headers["x-app-type"] === "portal") {
      return "admin";
    }
  }

  // Default to customer for customer app
  return "customer";
};

// Role-based authorization middleware
const requireRole = (allowedRoles) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      const userRole = req.user.user_type;

      // Admin has access to everything
      if (userRole === "admin") {
        return next();
      }

      // Check if user has required role
      if (allowedRoles.includes(userRole)) {
        return next();
      }

      return res.status(403).json({
        error: "Insufficient permissions",
        required: allowedRoles,
        current: userRole,
      });
    } catch (error) {
      logger.error("Error checking user role:", error);
      return res.status(500).json({ error: "Internal server error" });
    }
  };
};

// Check if user is customer
const requireCustomer = requireRole(["customer"]);

// Check if user is staff (admin, designer, or sales)
const requireStaff = requireRole(["admin", "designer", "sales"]);

// Check if user is admin
const requireAdmin = requireRole(["admin"]);

// Check if user is designer or admin
const requireDesigner = requireRole(["admin", "designer"]);

// Check if user is sales or admin
const requireSales = requireRole(["admin", "sales"]);

// Optional authentication - doesn't fail if no token provided
const optionalAuth = (req, res, next) => {
  const token = req.headers.authorization;

  if (!token) {
    return next();
  }

  // Use checkJwt middleware but don't fail on missing token
  checkJwt(req, res, (error) => {
    if (error && error.name === "UnauthorizedError") {
      // Continue without authentication
      return next();
    } else if (error) {
      return res.status(401).json({ error: "Invalid token" });
    }

    // If token is valid, sync user
    syncUser(req, res, next);
  });
};

// Middleware to check if user owns the resource
const requireOwnership = (userIdField = "customer_id") => {
  return (req, res, next) => {
    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    const currentUserId = req.user.id;
    const userRole = req.user.user_type;

    // Admin and staff can access any resource
    if (["admin", "designer", "sales"].includes(userRole)) {
      return next();
    }

    // Customer can only access their own resources
    if (resourceUserId && resourceUserId !== currentUserId) {
      return res.status(403).json({
        error: "Access denied - you can only access your own resources",
      });
    }

    next();
  };
};

// Auth0 Management API helpers
const auth0Helpers = {
  // Get user by Auth0 ID
  async getUserById(auth0Id) {
    try {
      return await managementClient.getUser({ id: auth0Id });
    } catch (error) {
      logger.error("Error fetching user from Auth0:", error);
      throw error;
    }
  },

  // Update user in Auth0
  async updateUser(auth0Id, userData) {
    try {
      return await managementClient.updateUser({ id: auth0Id }, userData);
    } catch (error) {
      logger.error("Error updating user in Auth0:", error);
      throw error;
    }
  },

  // Assign role to user
  async assignRole(auth0Id, roles) {
    try {
      return await managementClient.assignRolestoUser({ id: auth0Id }, { roles: Array.isArray(roles) ? roles : [roles] });
    } catch (error) {
      logger.error("Error assigning role to user:", error);
      throw error;
    }
  },

  // Get user roles
  async getUserRoles(auth0Id) {
    try {
      return await managementClient.getUserRoles({ id: auth0Id });
    } catch (error) {
      logger.error("Error fetching user roles:", error);
      throw error;
    }
  },

  // Block/unblock user
  async updateUserStatus(auth0Id, blocked = false) {
    try {
      return await managementClient.updateUser({ id: auth0Id }, { blocked });
    } catch (error) {
      logger.error("Error updating user status:", error);
      throw error;
    }
  },
};

module.exports = {
  checkJwt,
  syncUser,
  requireRole,
  requireCustomer,
  requireStaff,
  requireAdmin,
  requireDesigner,
  requireSales,
  optionalAuth,
  requireOwnership,
  managementClient,
  auth0Helpers,
};
