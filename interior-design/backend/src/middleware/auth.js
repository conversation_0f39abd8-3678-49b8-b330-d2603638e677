const { expressjwt: jwt } = require("express-jwt");
const jwksRsa = require("jwks-rsa");
const { ManagementClient } = require("auth0");
const logger = require("../utils/logger");
const { User } = require("../models");

// Auth0 JWT verification middleware
const checkJwt = jwt({
  secret: jwksRsa.expressJwtSecret({
    cache: true,
    rateLimit: true,
    jwksRequestsPerMinute: 5,
    jwksUri: `https://${process.env.AUTH0_DOMAIN}/.well-known/jwks.json`,
  }),
  audience: process.env.AUTH0_AUDIENCE,
  issuer: `https://${process.env.AUTH0_DOMAIN}/`,
  algorithms: ["RS256"],
  credentialsRequired: true,
});

// Auth0 Management Client for user operations
const managementClient = new ManagementClient({
  domain: process.env.AUTH0_DOMAIN,
  clientId: process.env.AUTH0_CLIENT_ID,
  clientSecret: process.env.AUTH0_CLIENT_SECRET,
  audience: process.env.AUTH0_MANAGEMENT_AUDIENCE,
  scope: "read:users update:users create:users delete:users",
});

// Middleware to sync Auth0 user with local database
const syncUser = async (req, res, next) => {
  try {
    if (!req.auth || !req.auth.sub) {
      return res.status(401).json({ error: "No user information found" });
    }

    const auth0Id = req.auth.sub;

    // Check if user exists in local database
    let user = await User.findOne({ where: { auth0_id: auth0Id } });

    if (!user) {
      // Get user info from Auth0
      const auth0User = await managementClient.getUser({ id: auth0Id });

      // Create user in local database
      user = await User.create({
        auth0_id: auth0Id,
        email: auth0User.email,
        first_name: auth0User.given_name || auth0User.nickname || "User",
        last_name: auth0User.family_name || "",
        user_type: determineUserType(auth0User),
        email_verified: auth0User.email_verified || false,
        is_active: true,
      });

      logger.info(`New user created: ${user.email}`);
    } else {
      // Update last login time
      await user.update({ updated_at: new Date() });
    }

    // Attach user to request object
    req.user = user;
    req.auth0User = req.auth;

    next();
  } catch (error) {
    logger.error("Error syncing user:", error);
    return res.status(500).json({
      error: "Internal server error during user authentication",
    });
  }
};

// Determine user type based on Auth0 user metadata or roles
const determineUserType = (auth0User) => {
  // Check for roles in Auth0 user metadata
  const roles = auth0User.app_metadata?.roles || [];
  const userType = auth0User.app_metadata?.user_type;

  if (userType) {
    return userType;
  }

  if (roles.includes("admin")) return "admin";
  if (roles.includes("designer")) return "designer";
  if (roles.includes("sales")) return "sales";

  // Default to customer
  return "customer";
};

// Role-based authorization middleware
const requireRole = (allowedRoles) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      const userRole = req.user.user_type;

      // Admin has access to everything
      if (userRole === "admin") {
        return next();
      }

      // Check if user has required role
      if (allowedRoles.includes(userRole)) {
        return next();
      }

      return res.status(403).json({
        error: "Insufficient permissions",
        required: allowedRoles,
        current: userRole,
      });
    } catch (error) {
      logger.error("Error checking user role:", error);
      return res.status(500).json({ error: "Internal server error" });
    }
  };
};

// Check if user is customer
const requireCustomer = requireRole(["customer"]);

// Check if user is staff (admin, designer, or sales)
const requireStaff = requireRole(["admin", "designer", "sales"]);

// Check if user is admin
const requireAdmin = requireRole(["admin"]);

// Check if user is designer or admin
const requireDesigner = requireRole(["admin", "designer"]);

// Check if user is sales or admin
const requireSales = requireRole(["admin", "sales"]);

// Optional authentication - doesn't fail if no token provided
const optionalAuth = (req, res, next) => {
  const token = req.headers.authorization;

  if (!token) {
    return next();
  }

  // Use checkJwt middleware but don't fail on missing token
  checkJwt(req, res, (error) => {
    if (error && error.name === "UnauthorizedError") {
      // Continue without authentication
      return next();
    } else if (error) {
      return res.status(401).json({ error: "Invalid token" });
    }

    // If token is valid, sync user
    syncUser(req, res, next);
  });
};

// Middleware to check if user owns the resource
const requireOwnership = (userIdField = "customer_id") => {
  return (req, res, next) => {
    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    const currentUserId = req.user.id;
    const userRole = req.user.user_type;

    // Admin and staff can access any resource
    if (["admin", "designer", "sales"].includes(userRole)) {
      return next();
    }

    // Customer can only access their own resources
    if (resourceUserId && resourceUserId !== currentUserId) {
      return res.status(403).json({
        error: "Access denied - you can only access your own resources",
      });
    }

    next();
  };
};

// Auth0 Management API helpers
const auth0Helpers = {
  // Get user by Auth0 ID
  async getUserById(auth0Id) {
    try {
      return await managementClient.getUser({ id: auth0Id });
    } catch (error) {
      logger.error("Error fetching user from Auth0:", error);
      throw error;
    }
  },

  // Update user in Auth0
  async updateUser(auth0Id, userData) {
    try {
      return await managementClient.updateUser({ id: auth0Id }, userData);
    } catch (error) {
      logger.error("Error updating user in Auth0:", error);
      throw error;
    }
  },

  // Assign role to user
  async assignRole(auth0Id, roles) {
    try {
      return await managementClient.assignRolestoUser({ id: auth0Id }, { roles: Array.isArray(roles) ? roles : [roles] });
    } catch (error) {
      logger.error("Error assigning role to user:", error);
      throw error;
    }
  },

  // Get user roles
  async getUserRoles(auth0Id) {
    try {
      return await managementClient.getUserRoles({ id: auth0Id });
    } catch (error) {
      logger.error("Error fetching user roles:", error);
      throw error;
    }
  },

  // Block/unblock user
  async updateUserStatus(auth0Id, blocked = false) {
    try {
      return await managementClient.updateUser({ id: auth0Id }, { blocked });
    } catch (error) {
      logger.error("Error updating user status:", error);
      throw error;
    }
  },
};

module.exports = {
  checkJwt,
  syncUser,
  requireRole,
  requireCustomer,
  requireStaff,
  requireAdmin,
  requireDesigner,
  requireSales,
  optionalAuth,
  requireOwnership,
  managementClient,
  auth0Helpers,
};
