const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const Redis = require('ioredis');
const logger = require('../utils/logger');

// Redis client for rate limiting (optional, falls back to memory store)
let redisClient;
try {
  if (process.env.REDIS_URL) {
    redisClient = new Redis(process.env.REDIS_URL);
    logger.info('Redis connected for rate limiting');
  }
} catch (error) {
  logger.warn('Redis not available, using memory store for rate limiting:', error.message);
}

// Create rate limiter with Redis store if available
const createRateLimiter = (options) => {
  const config = {
    ...options,
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    handler: (req, res) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip}, endpoint: ${req.originalUrl}`);
      res.status(429).json({
        success: false,
        message: 'Too many requests, please try again later.',
        retryAfter: Math.round(options.windowMs / 1000)
      });
    }
  };

  // Use Redis store if available
  if (redisClient) {
    config.store = new RedisStore({
      sendCommand: (...args) => redisClient.call(...args),
    });
  }

  return rateLimit(config);
};

// General API rate limiter
const generalLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Limit each IP to 1000 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health';
  }
});

// Authentication rate limiter (stricter)
const authLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 auth requests per windowMs
  message: 'Too many authentication attempts, please try again later.',
  skipSuccessfulRequests: true, // Don't count successful requests
});

// Password reset rate limiter (very strict)
const passwordResetLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 password reset requests per hour
  message: 'Too many password reset attempts, please try again later.',
});

// Registration rate limiter
const registrationLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit each IP to 5 registration attempts per hour
  message: 'Too many registration attempts, please try again later.',
});

// File upload rate limiter
const uploadLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Limit each IP to 50 upload requests per windowMs
  message: 'Too many upload requests, please try again later.',
});

// WhatsApp API rate limiter
const whatsappLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // Limit each IP to 10 WhatsApp requests per minute
  message: 'Too many WhatsApp requests, please try again later.',
});

// Search rate limiter
const searchLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 30, // Limit each IP to 30 search requests per minute
  message: 'Too many search requests, please try again later.',
});

// Admin operations rate limiter
const adminLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // Limit each IP to 200 admin requests per windowMs
  message: 'Too many admin requests, please try again later.',
});

// Contact form rate limiter
const contactLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit each IP to 5 contact form submissions per hour
  message: 'Too many contact form submissions, please try again later.',
});

// Newsletter subscription rate limiter
const newsletterLimiter = createRateLimiter({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 3, // Limit each IP to 3 newsletter subscriptions per day
  message: 'Too many newsletter subscription attempts, please try again later.',
});

// Product view rate limiter (for analytics)
const productViewLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // Limit each IP to 100 product views per minute
  message: 'Too many product view requests, please try again later.',
});

// API key based rate limiter for external integrations
const createApiKeyLimiter = (windowMs, max) => {
  return createRateLimiter({
    windowMs,
    max,
    keyGenerator: (req) => {
      // Use API key if available, otherwise fall back to IP
      return req.headers['x-api-key'] || req.ip;
    },
    message: 'API rate limit exceeded for this key.',
  });
};

// Webhook rate limiter
const webhookLimiter = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 100, // Limit each source to 100 webhook calls per minute
  message: 'Too many webhook requests, please try again later.',
  keyGenerator: (req) => {
    // Use webhook source identifier or IP
    return req.headers['x-webhook-source'] || req.ip;
  }
});

// Dynamic rate limiter based on user type
const createUserTypeLimiter = (limits) => {
  return (req, res, next) => {
    const userType = req.user?.userType || 'guest';
    const limit = limits[userType] || limits.default;
    
    const limiter = createRateLimiter({
      windowMs: limit.windowMs,
      max: limit.max,
      message: `Too many requests for ${userType} users, please try again later.`,
      keyGenerator: (req) => {
        // Use user ID if authenticated, otherwise IP
        return req.user?.userId || req.ip;
      }
    });

    return limiter(req, res, next);
  };
};

// User type specific limits
const userTypeLimits = {
  admin: { windowMs: 15 * 60 * 1000, max: 1000 },
  designer: { windowMs: 15 * 60 * 1000, max: 500 },
  sales: { windowMs: 15 * 60 * 1000, max: 300 },
  customer: { windowMs: 15 * 60 * 1000, max: 200 },
  default: { windowMs: 15 * 60 * 1000, max: 100 }
};

const userBasedLimiter = createUserTypeLimiter(userTypeLimits);

// Burst protection for specific endpoints
const burstProtection = createRateLimiter({
  windowMs: 1000, // 1 second
  max: 5, // Maximum 5 requests per second
  message: 'Request rate too high, please slow down.',
});

// Export all rate limiters
module.exports = {
  generalLimiter,
  authLimiter,
  passwordResetLimiter,
  registrationLimiter,
  uploadLimiter,
  whatsappLimiter,
  searchLimiter,
  adminLimiter,
  contactLimiter,
  newsletterLimiter,
  productViewLimiter,
  webhookLimiter,
  userBasedLimiter,
  burstProtection,
  createApiKeyLimiter,
  createRateLimiter,
  createUserTypeLimiter
};
