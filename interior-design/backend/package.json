{"name": "interior-design-backend", "version": "1.0.0", "description": "Backend API for Interior Design Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.1", "express-jwt": "^8.4.1", "jwks-rsa": "^3.1.0", "auth0": "^4.1.0", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "aws-sdk": "^2.1496.0", "uuid": "^9.0.1", "bcryptjs": "^2.4.3", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "morgan": "^1.10.0", "compression": "^1.7.4", "express-slow-down": "^2.0.1", "express-brute": "^1.0.1", "express-brute-redis": "^0.0.1", "redis": "^4.6.10", "nodemailer": "^6.9.7", "node-cron": "^3.0.3", "moment": "^2.29.4", "lodash": "^4.17.21", "sharp": "^0.33.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "@types/jest": "^29.5.8"}, "keywords": ["interior-design", "nodejs", "express", "auth0", "postgresql", "aws"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}