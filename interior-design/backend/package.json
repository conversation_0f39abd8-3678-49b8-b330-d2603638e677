{"name": "interior-design-backend", "version": "1.0.0", "description": "Backend API for Interior Design Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"auth0": "^4.1.0", "aws-sdk": "^2.1496.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-brute": "^1.0.1", "express-brute-redis": "^0.0.1", "express-jwt": "^8.4.1", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jwks-rsa": "^3.1.0", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "redis": "^4.6.10", "sequelize": "^6.35.1", "sharp": "^0.34.2", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.54.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["interior-design", "nodejs", "express", "auth0", "postgresql", "aws"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}